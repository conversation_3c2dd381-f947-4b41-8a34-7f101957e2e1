# 💩🎉TurdParty🎉💩 - Docker Compose Configuration
#
# ⚠️  CRITICAL DEPENDENCY WARNING ⚠️
# TurdParty REQUIRES Traefik to be running before starting these services!
#
# To start properly:
#   1. Start Traefik first: docker-compose -f traefik/docker-compose.yml up -d
#   2. Use startup script: ./scripts/start-turdparty.sh
#   3. OR check dependencies: ./scripts/check-traefik-dependency.sh
#
# Starting services without Traefik will cause failures!

services:
  api:
    container_name: turdpartycollab_api
    build:
      context: ./services/api
      dockerfile: Dockerfile
    # Host networking mode - API accessible on host port 8000
    # gRPC port 40000 accessible directly from host
    environment:
      - PYTHONPATH=/app
      - DEBUG=true
      - TEST_MODE=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOGSTASH_HOST=logstash
      - LOGSTASH_PORT=5000
      # MinIO Configuration
      - MINIO_HOST=storage
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false
      # Database Configuration
      - DATABASE_URL=********************************************/turdparty
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@database:5432/turdparty
      - POSTGRES_HOST=database
      - POSTGRES_PORT=5432
      - POSTGRES_DB=turdparty
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      # WebSocket Configuration
      - WEBSOCKET_HEARTBEAT_INTERVAL=30
      - METRICS_STREAMING_INTERVAL=1
      - MAX_CONCURRENT_STREAMS=100
      # File Transfer Settings
      - FILE_CHUNK_SIZE=65536
      - MAX_FILE_SIZE_MB=1024
      - UPLOAD_PROGRESS_UPDATE_INTERVAL=0.5
      # VM Communication
      - VM_COMMUNICATION_PROTOCOL=auto
      - SSH_FALLBACK_ENABLED=true
      - GRPC_RETRY_ATTEMPTS=3
      - VAGRANT_GRPC_PORT=40000
      - ENABLE_GRPC_COMMUNICATION=true
      # Testing
      - TESTING=false
      - LOG_LEVEL=INFO
    volumes:
      - ./services:/app/services
      - ./scripts:/app/scripts
      - ./archive/runtime-data/uploads:/app/uploads
      - ./archive/runtime-data/logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker VM management
      - ./tests:/app/tests  # Test files for development
    depends_on:
      - elasticsearch
      - logstash
      - database
      - redis
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    ports:
      - "8000:8000"  # API port for direct access and testing
      # Note: gRPC port 40000 conflicts with existing service, using expose only
    expose:
      - "8000"
      - "40000"  # gRPC port for Vagrant communication
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=api,component=backend,environment=development"
    labels:
      - "traefik.enable=true"

      # Main router - handles all traffic to the API service
      - "traefik.http.routers.api.rule=Host(`api.turdparty.localhost`)"
      - "traefik.http.routers.api.entrypoints=web"
      - "traefik.http.services.api.loadbalancer.server.port=8000"

      # Network configuration
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  elasticsearch:
    container_name: turdpartycollab_elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    # Port exposure for local-direct environment testing
    ports:
      - "9200:9200"
    expose:
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=elasticsearch,component=storage,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elasticsearch.rule=Host(`elasticsearch.turdparty.localhost`)"
      - "traefik.http.routers.elasticsearch.entrypoints=web"
      - "traefik.http.services.elasticsearch.loadbalancer.server.port=9200"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  logstash:
    container_name: turdpartycollab_logstash
    image: docker.elastic.co/logstash/logstash:8.11.0
    # No direct port exposure - using Traefik routing for web interface
    expose:
      - "5000"
      - "5044"
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./services/monitoring/logstash/pipeline/api-ecs-logs.conf:/usr/share/logstash/pipeline/api-ecs-logs.conf
      - ./services/monitoring/elasticsearch/templates:/usr/share/logstash/templates:ro
      - ./archive/runtime-data/logs:/app/logs
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=logstash,component=logging,environment=development"
    labels:
      - "traefik.enable=false"
      # Logstash port 5000/5044 are for log ingestion, not HTTP web interface
    healthcheck:
      test: ["CMD-SHELL", "netstat -tlnp | grep :5000 && netstat -tlnp | grep :5044 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  kibana:
    container_name: turdpartycollab_kibana
    image: docker.elastic.co/kibana/kibana:8.11.0
    # No direct port exposure - using Traefik routing
    expose:
      - "5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=kibana,component=visualization,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kibana.rule=Host(`kibana.turdparty.localhost`)"
      - "traefik.http.routers.kibana.entrypoints=web"
      - "traefik.http.services.kibana.loadbalancer.server.port=5601"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s

  # VM Monitoring Service
  vm-monitor:
    container_name: turdpartycollab_vm_monitor
    build:
      context: ./services/api
      dockerfile: Dockerfile
    command: ["python", "-c", "import time; print('VM Monitor service starting...'); [print(f'VM Monitor heartbeat {i}') or time.sleep(30) for i in range(999999)]"]
    environment:
      - PYTHONPATH=/app
      - DOCKER_HOST=unix:///var/run/docker.sock
      - MONITORING_INTERVAL=5
      - ALERT_THRESHOLDS_CPU=80
      - ALERT_THRESHOLDS_MEMORY=90
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOG_LEVEL=INFO
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./archive/runtime-data/logs:/app/logs
      - ./services:/app/services
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
    # Enable host networking for VM monitoring
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=vm-monitor,component=monitoring,environment=development"
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep -v grep | grep 'VM Monitor' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Status Dashboard Service
  status:
    container_name: turdpartycollab_status
    image: nginx:alpine
    volumes:
      - ./services/status:/usr/share/nginx/html:ro
      - ./services/status/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
      - elasticsearch
      - kibana
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=status,component=dashboard,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.status.rule=Host(`status.turdparty.localhost`)"
      - "traefik.http.routers.status.entrypoints=web"
      - "traefik.http.services.status.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://127.0.0.1:80/ || curl -f http://127.0.0.1:80/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Frontend Service
  frontend:
    container_name: turdpartycollab_frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=frontend,component=ui,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`frontend.turdparty.localhost`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

  # Documentation Service
  docs:
    container_name: turdpartycollab_docs
    image: nginx:alpine
    volumes:
      - ./docs/_build/html:/usr/share/nginx/html:ro
      - ./docs/nginx-docs.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=docs,component=documentation,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docs.rule=Host(`docs.turdparty.localhost`)"
      - "traefik.http.routers.docs.entrypoints=web"
      - "traefik.http.services.docs.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://127.0.0.1:80/ || curl -f http://127.0.0.1:80/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # PostgreSQL Database
  database:
    container_name: turdpartycollab_database
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=turdparty
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    expose:
      - "5432"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=database,component=storage,environment=development"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres && psql -U postgres -d postgres -c 'SELECT 1;' > /dev/null || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis for Celery message broker
  redis:
    container_name: turdpartycollab_redis
    image: redis:7-alpine
    expose:
      - "6379"
    volumes:
      - redis_data:/data
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=redis,component=cache,environment=development"
    labels:
      - "traefik.enable=false"
      # Redis is an internal service, not web-accessible
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep -q PONG && redis-cli info replication | grep -q 'role:master' || exit 1"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 10s

  # MinIO for file storage
  storage:
    container_name: turdpartycollab_storage
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    expose:
      - "9000"
      - "9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_BROWSER_REDIRECT_URL=http://storage.turdparty.localhost
    volumes:
      - minio_data:/data
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=minio,component=storage,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.storage.rule=Host(`storage.turdparty.localhost`)"
      - "traefik.http.routers.storage.entrypoints=web"
      - "traefik.http.services.storage.loadbalancer.server.port=9001"
      - "traefik.http.routers.storage-api.rule=Host(`storage-api.turdparty.localhost`)"
      - "traefik.http.routers.storage-api.entrypoints=web"
      - "traefik.http.services.storage-api.loadbalancer.server.port=9000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9000/minio/health/live && curl -f http://localhost:9000/minio/health/ready || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 20s

  # Celery Worker for background tasks
  celery-worker:
    container_name: turdpartycollab_celery_worker
    build:
      context: ./services/workers
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "celery_app", "worker", "--loglevel=info", "--concurrency=4", "--queues=default,file_ops,vm_ops,injection_ops,pool_ops,workflow_ops,elk_ops,maintenance,monitoring"]
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - DATABASE_URL=********************************************/turdparty
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@database:5432/turdparty
      - POSTGRES_HOST=database
      - POSTGRES_PORT=5432
      - POSTGRES_DB=turdparty
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - MINIO_ENDPOINT=storage:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LOG_LEVEL=INFO
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./archive/runtime-data/logs:/app/logs
      - ./services:/app/services:ro  # Read-only access to services modules
    depends_on:
      - redis
      - elasticsearch
      - storage
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=celery-worker,component=worker,environment=development"
    healthcheck:
      test: ["CMD-SHELL", "celery -A celery_app inspect ping || exit 1"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s

  # Celery Beat for scheduled tasks
  celery-beat:
    container_name: turdpartycollab_celery_beat
    build:
      context: ./services/workers
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "celery_app", "beat", "--loglevel=info", "--schedule=/app/celerybeat-data/celerybeat-schedule"]
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOG_LEVEL=INFO
    volumes:
      - celery_beat_data:/app/celerybeat-data
      - ./services:/app/services:ro  # Read-only access to services modules
    user: "1000:1000"
    depends_on:
      - redis
      - elasticsearch
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=celery-beat,component=scheduler,environment=development"
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep -v grep | grep 'celery.*beat' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Celery Flower for monitoring
  celery-flower:
    container_name: turdpartycollab_celery_flower
    build:
      context: ./services/workers
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "celery_app", "flower", "--port=5555"]
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    volumes:
      - ./services:/app/services:ro  # Read-only access to services modules
    expose:
      - "5555"
    depends_on:
      - redis
      - celery-worker
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=celery-flower,component=monitoring,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.flower.rule=Host(`flower.turdparty.localhost`)"
      - "traefik.http.routers.flower.entrypoints=web"
      - "traefik.http.services.flower.loadbalancer.server.port=5555"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5555/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Sphinx Reports Platform (temporarily disabled due to slow Docker build)
  # TODO: Fix Docker build performance by adding .dockerignore
  # reports:
  #   container_name: turdpartycollab_reports
  #   build:
  #     context: .
  #     dockerfile: docker/Dockerfile.reports
  #   expose:
  #     - "8080"
  #   volumes:
  #     - ./docs/reports:/app/docs/reports
  #     - ./docs/reports/_build:/app/docs/reports/_build
  #     - ./services:/app/services
  #   depends_on:
  #     - elasticsearch
  #     - api
  #   environment:
  #     - PYTHONPATH=/app
  #     - ELASTICSEARCH_URL=http://elasticsearch:9200
  #     - API_BASE_URL=http://api:8000
  #   networks:
  #     - turdpartycollab_net
  #     - traefik_network
  #   restart: unless-stopped
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "10m"
  #       max-file: "3"
  #       labels: "service=reports,component=documentation,environment=development"
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.reports.rule=Host(`reports.turdparty.localhost`)"
  #     - "traefik.http.routers.reports.entrypoints=web"
  #     - "traefik.http.services.reports.loadbalancer.server.port=8080"
  #     - "traefik.docker.network=traefik_network"
  #   healthcheck:
  #     test: ["CMD-SHELL", "curl -f http://localhost:8080/ || exit 1"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 45s

  # Filebeat for Docker log collection
  filebeat:
    container_name: turdpartycollab_filebeat
    image: docker.elastic.co/beats/filebeat:8.11.0
    user: root
    volumes:
      - ./config/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - LOGSTASH_HOSTS=logstash:5044
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=filebeat,component=log-collection,environment=development"
    command: ["filebeat", "-e", "-strict.perms=false"]
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep -v grep | grep filebeat || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
    name: turdpartycollab_postgres_data
    external: true
  elasticsearch_data:
    name: turdpartycollab_elasticsearch_data
    external: true
  filebeat_data:
    name: turdpartycollab_filebeat_data
    external: true
  redis_data:
    name: turdpartycollab_redis_data
    external: true
  minio_data:
    name: turdpartycollab_minio_data
    external: true
  celery_beat_data:
    name: turdpartycollab_celery_beat_data
    external: true

networks:
  turdpartycollab_net:
    name: turdpartycollab_net
    external: true
  traefik_network:
    name: traefik_network
    external: true
