#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Report Separation Test

Test script to verify that all reports are kept separate and properly organized
with unique naming, date-based directories, and proper archival.
"""

import json
import time
from datetime import datetime, timezone
from pathlib import Path
import subprocess
import sys


def test_report_separation():
    """Test that multiple report generations create separate files."""
    
    print("🧪 💩🎉TurdParty🎉💩 Report Separation Test")
    print("=" * 70)
    print("🔍 Testing report generation separation and organization")
    
    # Step 1: Check initial state
    print(f"\n📋 Step 1: Checking initial report state...")
    
    reports_dir = Path("docs/analysis-reports")
    date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    date_dir = reports_dir / date_str
    
    initial_files = []
    if date_dir.exists():
        initial_files = list(date_dir.glob("nodejs-analysis-*.rst"))
    
    print(f"    📁 Reports directory: {reports_dir}")
    print(f"    📅 Today's directory: {date_dir}")
    print(f"    📄 Initial Node.js reports: {len(initial_files)}")
    
    # Step 2: Generate first report
    print(f"\n🚀 Step 2: Generating first Node.js report...")
    
    result1 = subprocess.run(
        [sys.executable, "scripts/generate-nodejs-sphinx-report.py"],
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result1.returncode != 0:
        print(f"    ❌ First report generation failed: {result1.stderr}")
        return False
    
    # Check files after first generation
    files_after_first = list(date_dir.glob("nodejs-analysis-*.rst")) if date_dir.exists() else []
    first_report_file = None
    
    for file in files_after_first:
        if file not in initial_files:
            first_report_file = file
            break
    
    if first_report_file:
        print(f"    ✅ First report generated: {first_report_file.name}")
        print(f"    📊 File size: {first_report_file.stat().st_size} bytes")
    else:
        print(f"    ❌ First report file not found")
        return False
    
    # Step 3: Wait and generate second report
    print(f"\n⏳ Step 3: Waiting 2 seconds and generating second report...")
    time.sleep(2)
    
    result2 = subprocess.run(
        [sys.executable, "scripts/generate-nodejs-sphinx-report.py"],
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result2.returncode != 0:
        print(f"    ❌ Second report generation failed: {result2.stderr}")
        return False
    
    # Check files after second generation
    files_after_second = list(date_dir.glob("nodejs-analysis-*.rst")) if date_dir.exists() else []
    second_report_file = None
    
    for file in files_after_second:
        if file not in files_after_first:
            second_report_file = file
            break
    
    if second_report_file:
        print(f"    ✅ Second report generated: {second_report_file.name}")
        print(f"    📊 File size: {second_report_file.stat().st_size} bytes")
    else:
        print(f"    ❌ Second report file not found")
        return False
    
    # Step 4: Verify separation
    print(f"\n🔍 Step 4: Verifying report separation...")
    
    # Check that files are different
    files_are_different = first_report_file.name != second_report_file.name
    print(f"    ✅ Files have different names: {files_are_different}")
    
    # Check that both files exist
    both_exist = first_report_file.exists() and second_report_file.exists()
    print(f"    ✅ Both files exist: {both_exist}")
    
    # Check file sizes (should be similar but not identical due to timestamps)
    size1 = first_report_file.stat().st_size
    size2 = second_report_file.stat().st_size
    size_similar = abs(size1 - size2) < 1000  # Within 1KB difference
    print(f"    ✅ File sizes similar: {size_similar} ({size1} vs {size2} bytes)")
    
    # Check that content is similar but timestamps are different
    with open(first_report_file, 'r') as f:
        content1 = f.read()
    with open(second_report_file, 'r') as f:
        content2 = f.read()
    
    # Extract timestamps from content
    timestamp1 = extract_timestamp_from_content(content1)
    timestamp2 = extract_timestamp_from_content(content2)
    
    timestamps_different = timestamp1 != timestamp2
    print(f"    ✅ Timestamps different: {timestamps_different}")
    if timestamp1 and timestamp2:
        print(f"       📅 First: {timestamp1}")
        print(f"       📅 Second: {timestamp2}")
    
    # Step 5: Check index integration
    print(f"\n📋 Step 5: Checking index integration...")
    
    index_file = reports_dir / "index.rst"
    if index_file.exists():
        with open(index_file, 'r') as f:
            index_content = f.read()
        
        first_in_index = first_report_file.stem in index_content
        second_in_index = second_report_file.stem in index_content
        
        print(f"    ✅ First report in index: {first_in_index}")
        print(f"    ✅ Second report in index: {second_in_index}")
        
        index_integration = first_in_index and second_in_index
    else:
        print(f"    ❌ Index file not found")
        index_integration = False
    
    # Step 6: Overall assessment
    print(f"\n🎯 Step 6: Overall assessment...")
    
    all_tests_passed = (
        files_are_different and
        both_exist and
        size_similar and
        timestamps_different and
        index_integration
    )
    
    if all_tests_passed:
        print(f"    🎉 ALL TESTS PASSED!")
        print(f"    ✅ Reports are properly separated")
        print(f"    ✅ Unique naming system working")
        print(f"    ✅ Date-based organization functional")
        print(f"    ✅ Index integration successful")
        print(f"    ✅ No report overwrites detected")
    else:
        print(f"    ❌ SOME TESTS FAILED!")
        print(f"    ⚠️ Report separation may have issues")
    
    return all_tests_passed


def extract_timestamp_from_content(content: str) -> str:
    """Extract timestamp from report content."""
    lines = content.split('\n')
    for line in lines:
        if 'Generated:' in line or 'Timestamp:' in line:
            return line.strip()
    return ""


def test_archival_system():
    """Test the archival system functionality."""
    
    print(f"\n🗄️ Testing archival system...")
    
    try:
        result = subprocess.run(
            [sys.executable, "scripts/report-archival-system.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print(f"    ✅ Archival system test passed")
            # Show last few lines of output
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines[-3:]:
                print(f"       {line}")
            return True
        else:
            print(f"    ❌ Archival system test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"    ⚠️ Archival system not available: {e}")
        return True  # Not critical for basic functionality


def main():
    """Main test function."""
    
    try:
        # Test report separation
        separation_success = test_report_separation()
        
        # Test archival system
        archival_success = test_archival_system()
        
        overall_success = separation_success and archival_success
        
        print(f"\n{'='*70}")
        if overall_success:
            print("🎉 REPORT SEPARATION TEST: SUCCESS")
            print("✅ All reports are kept separate and properly organized")
            print("✅ Unique naming system prevents overwrites")
            print("✅ Date-based directory structure working")
            print("✅ Index integration maintains proper references")
            print("✅ Archival system ready for historical management")
            print("📚 Professional report management system operational")
        else:
            print("❌ REPORT SEPARATION TEST: FAILED")
            if not separation_success:
                print("⚠️ Report separation system has issues")
            if not archival_success:
                print("⚠️ Archival system needs attention")
            print("🔧 Further investigation needed")
        
        print(f"{'='*70}")
        
        return 0 if overall_success else 1
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
