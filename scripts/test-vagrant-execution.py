#!/usr/bin/env python3
"""
Test Vagrant SSH Execution for Node.js VM

This script tests whether we can execute the Node.js installer
via Vagrant SSH to verify post-injection execution works.
"""

import subprocess
import time
import requests
import json
import sys
import os
from pathlib import Path

# Add utils to path for ServiceURLManager
sys.path.insert(0, str(Path(__file__).parent.parent / "utils"))
from service_urls import ServiceURLManager

def find_vagrant_vm_directory(vm_name_pattern):
    """Find the Vagrant VM directory for the Node.js VM."""
    
    # Common Vagrant VM locations
    possible_paths = [
        "/tmp/turdparty_vms",
        "/home/<USER>",
        "/var/lib/vagrant",
        Path.home() / "turdparty_vms"
    ]
    
    for base_path in possible_paths:
        if isinstance(base_path, str):
            base_path = Path(base_path)
        
        if base_path.exists():
            print(f"    🔍 Checking {base_path}")
            for vm_dir in base_path.iterdir():
                if vm_dir.is_dir() and vm_name_pattern in vm_dir.name:
                    print(f"    ✅ Found VM directory: {vm_dir}")
                    return str(vm_dir)
    
    return None

def test_vagrant_execution():
    """Test execution via Vagrant SSH."""
    
    print("🧪 💩🎉TurdParty🎉💩 Vagrant SSH Execution Test")
    print("=" * 60)
    
    # Node.js VM details
    vm_name = "win10-nodejs-a0e6de53"
    vm_id = "98ae8d3a-dc81-4d39-acd1-831d418f9bed"
    injected_file = "C:\\TurdParty\\node-v20.10.0-x64.msi"
    
    print(f"🎯 Target VM: {vm_name}")
    print(f"📁 Injected File: {injected_file}")
    
    # Step 1: Find the Vagrant VM directory
    print(f"\n📋 Step 1: Finding Vagrant VM directory...")
    vm_dir = find_vagrant_vm_directory("nodejs")
    
    if not vm_dir:
        print(f"    ❌ Could not find Vagrant VM directory")
        return False
    
    print(f"    ✅ VM Directory: {vm_dir}")
    
    # Step 2: Test Vagrant connectivity
    print(f"\n🔗 Step 2: Testing Vagrant SSH connectivity...")
    try:
        result = subprocess.run(
            ["vagrant", "status"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print(f"    ✅ Vagrant status successful")
            print(f"    📋 Status: {result.stdout.strip()}")
        else:
            print(f"    ⚠️ Vagrant status returned {result.returncode}")
            print(f"    📝 Output: {result.stdout}")
            print(f"    📝 Error: {result.stderr}")
    except Exception as e:
        print(f"    ❌ Error checking Vagrant status: {e}")
        return False
    
    # Step 3: Execute the Node.js installer
    print(f"\n⚙️ Step 3: Executing Node.js installer via Vagrant SSH...")
    
    # PowerShell command to run the MSI installer
    ps_command = f'''
    Set-Location "C:\\TurdParty"
    Write-Host "Starting Node.js installation..."
    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", "node-v20.10.0-x64.msi", "/quiet", "/norestart" -Wait -PassThru
    Write-Host "Installation completed with exit code: $($process.ExitCode)"
    Get-ChildItem "C:\\Program Files\\nodejs" -ErrorAction SilentlyContinue | Select-Object Name
    '''
    
    try:
        print(f"    🔧 Executing PowerShell command...")
        result = subprocess.run(
            ["vagrant", "ssh", "-c", f'powershell.exe -Command "{ps_command}"'],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=600,  # 10 minutes timeout
            check=False
        )
        
        print(f"    📤 Return Code: {result.returncode}")
        print(f"    📝 Output: {result.stdout}")
        if result.stderr:
            print(f"    ⚠️ Errors: {result.stderr}")
        
        if result.returncode == 0:
            print(f"    ✅ Command executed successfully!")
            
            # Wait for ECS events to be generated
            print(f"\n⏳ Step 4: Waiting 60 seconds for ECS events...")
            time.sleep(60)
            
            # Check for new ECS events
            return check_ecs_events_after_execution(vm_id)
        else:
            print(f"    ❌ Command execution failed")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"    ⏰ Command execution timed out")
        return False
    except Exception as e:
        print(f"    ❌ Error executing command: {e}")
        return False

def check_ecs_events_after_execution(vm_id):
    """Check if ECS events were generated after manual execution."""
    
    print(f"\n🔍 Step 5: Checking for new ECS events...")
    
    elasticsearch_url = "http://elasticsearch.turdparty.localhost"
    
    # Search for recent events from this VM
    search_query = {
        "query": {
            "bool": {
                "must": [
                    {"match": {"vm_id": vm_id}},
                    {"range": {"@timestamp": {"gte": "now-5m"}}}  # Last 5 minutes
                ]
            }
        },
        "size": 10,
        "sort": [{"@timestamp": {"order": "desc"}}]
    }
    
    try:
        response = requests.post(
            f"{elasticsearch_url}/turdparty-*/_search",
            headers={"Content-Type": "application/json"},
            json=search_query,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            total_events = data.get("hits", {}).get("total", {}).get("value", 0)
            
            print(f"    📊 New ECS events found: {total_events}")
            
            if total_events > 0:
                print(f"    ✅ SUCCESS: Manual execution generated ECS events!")
                
                # Show sample events
                for i, hit in enumerate(data.get("hits", {}).get("hits", [])[:3]):
                    source = hit.get("_source", {})
                    timestamp = source.get("@timestamp", "unknown")
                    event_action = source.get("event", {}).get("action", "unknown")
                    print(f"    📋 Event {i+1}: {timestamp} - {event_action}")
                
                return True
            else:
                print(f"    ⚠️ No new ECS events found")
                return False
        else:
            print(f"    ❌ Failed to query Elasticsearch: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ Error checking ECS events: {e}")
        return False

def main():
    """Main test function."""
    
    success = test_vagrant_execution()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 VAGRANT EXECUTION TEST: SUCCESS")
        print("✅ Manual execution via Vagrant SSH generated ECS events")
        print("💡 Root cause confirmed: Post-injection execution not triggered")
        print("🔧 Solution: Fix Celery workers to process post-injection tasks")
    else:
        print("❌ VAGRANT EXECUTION TEST: FAILED")
        print("⚠️ Manual execution did not work as expected")
        print("🔍 Check Vagrant VM status and SSH connectivity")
    
    print(f"{'='*60}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
