#!/usr/bin/env python3
"""
Test Simple File Injection and Execution

This script tests post-injection execution using an existing VM
and a simple test file to verify Celery workers are functioning.
"""

import requests
import time
import json
import sys
import tempfile
import os
from pathlib import Path

# Add utils to path for ServiceURLManager
sys.path.insert(0, str(Path(__file__).parent.parent / "utils"))
from service_urls import ServiceURLManager

def create_test_file():
    """Create a simple test executable file."""
    
    # Create a simple batch file that creates a test file
    test_content = """@echo off
echo TurdParty Test Execution > C:\\TurdParty\\test-execution-result.txt
echo Execution Time: %date% %time% >> C:\\TurdParty\\test-execution-result.txt
echo Post-injection execution is working! >> C:\\TurdParty\\test-execution-result.txt
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
        f.write(test_content)
        return f.name

def upload_test_file(api_base, file_path):
    """Upload test file to TurdParty API."""
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': ('test-execution.bat', f, 'application/octet-stream')}
            data = {
                'description': 'Test file for post-injection execution verification'
            }
            
            response = requests.post(
                f"{api_base}/files/upload",
                files=files,
                data=data,
                timeout=60
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                return result.get('file_id')
            else:
                print(f"    ❌ File upload failed: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"    ❌ Error uploading file: {e}")
        return None
    finally:
        # Clean up temporary file
        try:
            os.unlink(file_path)
        except:
            pass

def test_simple_injection():
    """Test simple file injection and execution."""
    
    print("🧪 💩🎉TurdParty🎉💩 Simple Post-Injection Test")
    print("=" * 60)
    
    # Use ServiceURLManager for proper Traefik routing
    url_manager = ServiceURLManager("development")
    api_base = url_manager.get_service_url("api") + "/api/v1"
    
    # Use existing Node.js VM
    vm_id = "98ae8d3a-dc81-4d39-acd1-831d418f9bed"
    vm_name = "win10-nodejs-a0e6de53"
    
    print(f"🎯 Using existing VM: {vm_name}")
    print(f"📁 VM ID: {vm_id}")
    
    # Step 1: Create and upload test file
    print(f"\n📋 Step 1: Creating and uploading test file...")
    test_file_path = create_test_file()
    print(f"    ✅ Created test batch file: {test_file_path}")
    
    file_id = upload_test_file(api_base, test_file_path)
    if not file_id:
        return False
    
    print(f"    ✅ Uploaded test file: {file_id}")
    
    # Step 2: Check VM status
    print(f"\n📋 Step 2: Checking VM status...")
    try:
        response = requests.get(f"{api_base}/vms/{vm_id}", timeout=30)
        if response.status_code == 200:
            vm_data = response.json()
            status = vm_data.get("status", "unknown")
            print(f"    ✅ VM Status: {status}")
            
            if status not in ["running", "monitoring"]:
                print(f"    ⚠️ VM is not in a ready state")
                return False
        else:
            print(f"    ❌ Failed to get VM status: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ Error checking VM: {e}")
        return False
    
    # Step 3: Inject file with execute_after_injection=True
    print(f"\n💉 Step 3: Injecting test file with execution enabled...")
    injection_config = {
        "file_id": file_id,
        "injection_path": "C:\\TurdParty\\test-execution.bat",
        "execute_after_injection": True,  # This is the key test!
        "permissions": "0755"
    }
    
    try:
        response = requests.post(
            f"{api_base}/vms/{vm_id}/inject",
            json=injection_config,
            timeout=120
        )
        
        if response.status_code in [200, 201]:
            injection_result = response.json()
            print(f"    ✅ File injection request successful!")
            print(f"    📁 Target path: {injection_config['injection_path']}")
            print(f"    ⚙️ Execute after injection: {injection_config['execute_after_injection']}")
        else:
            print(f"    ❌ File injection failed: {response.status_code}")
            print(f"    📝 Response: {response.text}")
            return False
    except Exception as e:
        print(f"    ❌ Error injecting file: {e}")
        return False
    
    # Step 4: Wait and check for execution results
    print(f"\n🔍 Step 4: Waiting for post-injection execution...")
    print(f"    ⏳ Waiting 60 seconds for Celery workers to process...")
    time.sleep(60)
    
    # Check for ECS events
    print(f"\n📊 Step 5: Checking for ECS events...")
    elasticsearch_url = "http://elasticsearch.turdparty.localhost"
    search_query = {
        "query": {
            "bool": {
                "must": [
                    {"match": {"vm_id": vm_id}},
                    {"range": {"@timestamp": {"gte": "now-5m"}}}  # Last 5 minutes
                ]
            }
        },
        "size": 10,
        "sort": [{"@timestamp": {"order": "desc"}}]
    }
    
    try:
        response = requests.post(
            f"{elasticsearch_url}/turdparty-*/_search",
            headers={"Content-Type": "application/json"},
            json=search_query,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            total_events = data.get("hits", {}).get("total", {}).get("value", 0)
            
            print(f"    📊 Recent ECS events found: {total_events}")
            
            # Look for injection events
            injection_events = 0
            execution_events = 0
            
            for hit in data.get("hits", {}).get("hits", []):
                source = hit.get("_source", {})
                action = source.get("event", {}).get("action", "")
                
                if "injection" in action:
                    injection_events += 1
                elif "execution" in action or "process" in action:
                    execution_events += 1
            
            print(f"    📋 Injection events: {injection_events}")
            print(f"    📋 Execution events: {execution_events}")
            
            if injection_events > 0:
                print(f"    ✅ File injection was logged to ECS")
                
                if execution_events > 0:
                    print(f"    ✅ SUCCESS: Post-injection execution detected!")
                    return True
                else:
                    print(f"    ⚠️ Injection logged but no execution events found")
                    print(f"    💡 This suggests Celery workers processed injection but not execution")
                    return False
            else:
                print(f"    ⚠️ No recent injection events found")
                return False
        else:
            print(f"    ❌ Failed to query Elasticsearch: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ Error checking ECS events: {e}")
        return False

def main():
    """Main test function."""
    
    success = test_simple_injection()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 SIMPLE POST-INJECTION TEST: SUCCESS")
        print("✅ Celery workers are processing injection tasks")
        print("✅ Post-injection execution is working")
        print("✅ ECS events are being generated")
        print("💡 The enhanced analysis should now work!")
    else:
        print("❌ SIMPLE POST-INJECTION TEST: FAILED")
        print("⚠️ Post-injection execution is not working yet")
        print("🔧 Celery workers may need further fixes")
    
    print(f"{'='*60}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
