#!/usr/bin/env python3
"""
Test API-triggered Sphinx Document Generation

This script tests the new API endpoint for generating Sphinx documents
from ECS logs and Node.js injection test outcomes.
"""

import requests
import time
import json
import sys
from pathlib import Path

# Add utils to path for ServiceURLManager
sys.path.insert(0, str(Path(__file__).parent.parent / "utils"))
from service_urls import ServiceURLManager

def test_api_sphinx_generation():
    """Test the API endpoint for Sphinx document generation."""

    print("🚀 💩🎉TurdParty🎉💩 API Sphinx Generation Test")
    print("=" * 70)
    print("🌐 Testing API-triggered Sphinx document generation")
    print("📊 Using ECS logs from Node.js injection tests")

    # Initialize ServiceURLManager
    url_manager = ServiceURLManager("development")
    api_url = url_manager.get_service_url("api")

    print(f"🔗 API URL: {api_url}")

    # Step 1: Test the new admin endpoint
    print(f"\n📋 Step 1: Calling Sphinx report generation API...")

    # Use ServiceURLManager to get the proper endpoint
    try:
        # Try to get admin endpoint from config, fallback to manual construction
        endpoint = url_manager.get_api_endpoint("admin", "sphinx_generate")
    except (ValueError, KeyError):
        # Fallback to manual construction if not in config
        endpoint = f"{api_url}/api/v1/admin/reports/sphinx/generate"

    print(f"    🔗 Endpoint: {endpoint}")

    # Step 1a: Test if admin health endpoint works first
    print(f"\n🔍 Step 1a: Testing admin health endpoint...")
    try:
        health_endpoint = f"{api_url}/api/v1/admin/health"
        response = requests.get(health_endpoint, timeout=10)
        print(f"    📊 Admin health status: {response.status_code}")
        if response.status_code != 200:
            print(f"    ⚠️ Admin endpoints may not be available")
            print(f"    💡 Falling back to direct script execution...")
            return test_direct_sphinx_generation()
    except Exception as e:
        print(f"    ❌ Admin health check failed: {e}")
        print(f"    💡 Falling back to direct script execution...")
        return test_direct_sphinx_generation()
    
    # Request payload for Node.js report generation
    payload = {
        "file_uuid": "55505f01-539a-4d96-98bd-8b7ed8bac0a4",  # Node.js file UUID from previous test
        "binary_name": "node-v20.10.0-x64.msi",
        "report_type": "nodejs",
        "include_ecs_logs": True,
        "build_html": True
    }
    
    try:
        response = requests.post(
            endpoint,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code in [200, 201, 202]:
            result = response.json()
            task_id = result.get("task_id")
            
            print(f"    ✅ API call successful!")
            print(f"    📋 Task ID: {task_id}")
            print(f"    📄 Report Type: {result.get('details', {}).get('report_type')}")
            print(f"    🔍 Include ECS Logs: {result.get('details', {}).get('include_ecs_logs')}")
            print(f"    🔨 Build HTML: {result.get('details', {}).get('build_html')}")
            
            # Step 2: Wait for task completion
            print(f"\n⏳ Step 2: Waiting for Sphinx generation to complete...")
            print(f"    ⏱️ Waiting 90 seconds for background task...")
            time.sleep(90)
            
            # Step 3: Check if documentation was generated
            print(f"\n📄 Step 3: Checking generated documentation...")
            
            # Check if the RST file was created
            rst_file = Path("docs/analysis-reports/nodejs-analysis-detailed.rst")
            html_file = Path("docs/analysis-reports/_build/html/nodejs-analysis-detailed.html")
            
            if rst_file.exists():
                print(f"    ✅ RST file generated: {rst_file}")
                
                # Check file size and modification time
                stat = rst_file.stat()
                print(f"    📊 File size: {stat.st_size} bytes")
                print(f"    📅 Modified: {time.ctime(stat.st_mtime)}")
                
                if html_file.exists():
                    print(f"    ✅ HTML file generated: {html_file}")
                    print(f"    🌐 View at: http://localhost:8080/analysis-reports/nodejs-analysis-detailed.html")
                else:
                    print(f"    ⚠️ HTML file not found: {html_file}")
                
                return True
            else:
                print(f"    ❌ RST file not found: {rst_file}")
                return False
                
        else:
            print(f"    ❌ API call failed: {response.status_code}")
            print(f"    📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"    ❌ Error calling API: {e}")
        return False

def verify_ecs_data_integration():
    """Verify that the generated document includes ECS data."""
    
    print(f"\n🔍 Step 4: Verifying ECS data integration...")
    
    rst_file = Path("docs/analysis-reports/nodejs-analysis-detailed.rst")
    
    if not rst_file.exists():
        print(f"    ❌ RST file not found for verification")
        return False
    
    try:
        with open(rst_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for ECS-related content
        ecs_indicators = [
            "ECS Data Summary",
            "Total Events",
            "Event Distribution", 
            "Elasticsearch",
            "turdparty-*",
            "event_categories"
        ]
        
        found_indicators = []
        for indicator in ecs_indicators:
            if indicator in content:
                found_indicators.append(indicator)
        
        print(f"    📊 ECS indicators found: {len(found_indicators)}/{len(ecs_indicators)}")
        
        for indicator in found_indicators:
            print(f"       ✅ {indicator}")
        
        if len(found_indicators) >= 4:  # At least 4 out of 6 indicators
            print(f"    ✅ ECS data successfully integrated into Sphinx document")
            return True
        else:
            print(f"    ⚠️ Limited ECS data integration detected")
            return False
            
    except Exception as e:
        print(f"    ❌ Error reading RST file: {e}")
        return False

def test_direct_sphinx_generation():
    """Test direct Sphinx generation by calling the script directly."""

    print(f"\n🔄 Direct Sphinx Generation Test")
    print(f"    💡 Calling generate-nodejs-sphinx-report.py directly...")

    try:
        import subprocess
        import sys

        # Call the script directly
        result = subprocess.run(
            [sys.executable, "scripts/generate-nodejs-sphinx-report.py"],
            capture_output=True,
            text=True,
            timeout=120
        )

        if result.returncode == 0:
            print(f"    ✅ Direct script execution successful!")
            print(f"    📄 Script output preview:")
            # Show last few lines of output
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines[-5:]:
                print(f"       {line}")
            return True
        else:
            print(f"    ❌ Direct script execution failed: {result.returncode}")
            print(f"    📝 Error: {result.stderr}")
            return False

    except Exception as e:
        print(f"    ❌ Error executing script directly: {e}")
        return False

def main():
    """Main test function."""
    
    try:
        # Test API-triggered Sphinx generation
        api_success = test_api_sphinx_generation()
        
        # Verify ECS data integration
        ecs_success = verify_ecs_data_integration()
        
        overall_success = api_success and ecs_success
        
        print(f"\n{'='*70}")
        if overall_success:
            print("🎉 API SPHINX GENERATION TEST: SUCCESS")
            print("✅ API endpoint successfully triggered Sphinx generation")
            print("✅ Comprehensive documentation created from ECS logs")
            print("✅ Node.js injection test outcomes properly documented")
            print("📚 Professional-grade Sphinx report with ECS integration")
            print("🌐 API-driven documentation generation working correctly")
        else:
            print("❌ API SPHINX GENERATION TEST: FAILED")
            if not api_success:
                print("⚠️ API endpoint failed to generate documentation")
            if not ecs_success:
                print("⚠️ ECS data integration incomplete")
            print("🔧 Further investigation needed")
        
        print(f"{'='*70}")
        
        return 0 if overall_success else 1
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
