#!/usr/bin/env python3
"""
Test Post-Injection Execution Fix

This script tests whether the Celery worker fixes have resolved
the post-injection execution issue by running a simple binary analysis.
"""

import requests
import time
import json
import sys
from pathlib import Path

# Add utils to path for ServiceURLManager
sys.path.insert(0, str(Path(__file__).parent.parent / "utils"))
from service_urls import ServiceURLManager

def test_post_injection_fix():
    """Test if post-injection execution is now working."""
    
    print("🧪 💩🎉TurdParty🎉💩 Post-Injection Execution Fix Test")
    print("=" * 70)
    
    # Use ServiceURLManager for proper Traefik routing
    url_manager = ServiceURLManager("development")
    api_base = url_manager.get_service_url("api") + "/api/v1"
    
    # Test with a simple binary that should execute quickly
    test_binary = {
        "name": "notepadpp",
        "description": "Notepad++ text editor",
        "filename": "npp.8.5.8.Installer.x64.exe",
        "download_url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
        "expected_size_mb": 4.5,
        "install_command": "/S"  # Silent installation
    }
    
    print(f"🎯 Testing with: {test_binary['name']}")
    print(f"📝 Description: {test_binary['description']}")
    
    # Step 1: Check if file already exists in MinIO
    print(f"\n📋 Step 1: Checking MinIO for existing file...")
    try:
        response = requests.get(f"{api_base}/files/", timeout=30)
        if response.status_code == 200:
            files = response.json().get("files", [])
            existing_file = None
            for file_info in files:
                if test_binary["filename"] in file_info.get("filename", ""):
                    existing_file = file_info
                    break
            
            if existing_file:
                print(f"    ✅ Found existing file: {existing_file['filename']}")
                print(f"    📁 File ID: {existing_file['file_id']}")
                file_id = existing_file['file_id']
            else:
                print(f"    📭 File not found in MinIO, would need to download")
                print(f"    ⚠️ Skipping download for this test")
                return False
        else:
            print(f"    ❌ Failed to check MinIO: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ Error checking MinIO: {e}")
        return False
    
    # Step 2: Create a Windows 10 VM
    print(f"\n🏠 Step 2: Creating Windows 10 VM...")
    vm_config = {
        "name": f"test-post-injection-{int(time.time())}",
        "template": "10Baht/windows10-turdparty",
        "memory_mb": 4096,
        "cpus": 2,
        "type": "vagrant"
    }
    
    try:
        response = requests.post(
            f"{api_base}/vms/",
            json=vm_config,
            timeout=60
        )
        
        if response.status_code in [200, 201]:
            vm_data = response.json()
            vm_id = vm_data["vm_id"]
            print(f"    ✅ VM created: {vm_id}")
            print(f"    📝 VM name: {vm_data['name']}")
        else:
            print(f"    ❌ Failed to create VM: {response.status_code}")
            print(f"    📝 Response: {response.text}")
            return False
    except Exception as e:
        print(f"    ❌ Error creating VM: {e}")
        return False
    
    # Step 3: Wait for VM to be ready
    print(f"\n⏳ Step 3: Waiting for VM to be ready...")
    max_wait = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{api_base}/vms/{vm_id}", timeout=30)
            if response.status_code == 200:
                vm_status = response.json()
                status = vm_status.get("status", "unknown")
                print(f"    🔄 VM Status: {status}")
                
                if status in ["running", "monitoring"]:
                    print(f"    ✅ VM is ready!")
                    break
                elif status in ["failed", "error"]:
                    print(f"    ❌ VM creation failed")
                    return False
            
            time.sleep(10)
        except Exception as e:
            print(f"    ⚠️ Error checking VM status: {e}")
            time.sleep(10)
    else:
        print(f"    ⏰ VM creation timed out")
        return False
    
    # Step 4: Inject file with execute_after_injection=True
    print(f"\n💉 Step 4: Injecting file with post-execution enabled...")
    injection_config = {
        "file_id": file_id,
        "injection_path": f"C:\\TurdParty\\{test_binary['filename']}",
        "execute_after_injection": True,  # This is the key setting!
        "permissions": "0755"
    }
    
    try:
        response = requests.post(
            f"{api_base}/vms/{vm_id}/inject",
            json=injection_config,
            timeout=120
        )
        
        if response.status_code in [200, 201]:
            injection_result = response.json()
            print(f"    ✅ File injection successful!")
            print(f"    📁 Target path: {injection_config['injection_path']}")
            print(f"    ⚙️ Execute after injection: {injection_config['execute_after_injection']}")
        else:
            print(f"    ❌ File injection failed: {response.status_code}")
            print(f"    📝 Response: {response.text}")
            return False
    except Exception as e:
        print(f"    ❌ Error injecting file: {e}")
        return False
    
    # Step 5: Wait and check for ECS events
    print(f"\n🔍 Step 5: Waiting for post-injection execution and ECS events...")
    print(f"    ⏳ Waiting 120 seconds for execution and monitoring...")
    time.sleep(120)
    
    # Check for ECS events
    elasticsearch_url = "http://elasticsearch.turdparty.localhost"
    search_query = {
        "query": {
            "bool": {
                "must": [
                    {"match": {"vm_id": vm_id}},
                    {"range": {"@timestamp": {"gte": "now-10m"}}}  # Last 10 minutes
                ]
            }
        },
        "size": 20,
        "sort": [{"@timestamp": {"order": "desc"}}]
    }
    
    try:
        response = requests.post(
            f"{elasticsearch_url}/turdparty-*/_search",
            headers={"Content-Type": "application/json"},
            json=search_query,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            total_events = data.get("hits", {}).get("total", {}).get("value", 0)
            
            print(f"    📊 Total ECS events found: {total_events}")
            
            if total_events > 1:  # More than just the injection event
                print(f"    ✅ SUCCESS: Post-injection execution generated ECS events!")
                
                # Show sample events
                events_by_action = {}
                for hit in data.get("hits", {}).get("hits", []):
                    source = hit.get("_source", {})
                    action = source.get("event", {}).get("action", "unknown")
                    events_by_action[action] = events_by_action.get(action, 0) + 1
                
                print(f"    📋 Event types found:")
                for action, count in events_by_action.items():
                    print(f"       - {action}: {count} events")
                
                return True
            else:
                print(f"    ⚠️ Only injection event found - post-execution may not have worked")
                return False
        else:
            print(f"    ❌ Failed to query Elasticsearch: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ Error checking ECS events: {e}")
        return False

def main():
    """Main test function."""
    
    success = test_post_injection_fix()
    
    print(f"\n{'='*70}")
    if success:
        print("🎉 POST-INJECTION EXECUTION FIX: SUCCESS")
        print("✅ Celery workers are now processing post-injection tasks")
        print("✅ Files are being executed after injection")
        print("✅ ECS events are being generated from actual software execution")
        print("💡 The Node.js analysis should now work properly!")
    else:
        print("❌ POST-INJECTION EXECUTION FIX: FAILED")
        print("⚠️ Post-injection execution is still not working")
        print("🔍 Further investigation needed")
    
    print(f"{'='*70}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
