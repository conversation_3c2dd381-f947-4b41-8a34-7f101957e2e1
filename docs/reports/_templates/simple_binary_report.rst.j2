{{ filename }} Binary Analysis Report
{{ '=' * (filename|length + 25) }}

.. meta::
   :description: Comprehensive analysis of {{ filename }} execution in Windows VM environment
   :keywords: {{ filename|lower }}, binary analysis, installation footprint, security assessment

Executive Summary
-----------------

.. admonition:: 🎯 Analysis Overview
   :class: note

   **Binary**: {{ filename }}  
   **Size**: {{ "%.1f"|format(report.file_info.file_size_bytes / 1024 / 1024) }} MB  
   **Risk Level**: {{ risk_level.upper() }}  
   **Total Events**: {{ total_events }}

The {{ filename }} represents a legitimate software application with standard installation behavior. 
{% if total_events == 0 %}
No ECS events were captured during analysis, indicating either a silent installation or limited monitoring coverage.
{% else %}
Analysis captured {{ total_events }} events during execution.
{% endif %}

File Information
----------------

.. list-table:: Binary Metadata
   :header-rows: 1
   :widths: 25 75

   * - Property
     - Value
   * - **Filename**
     - {{ filename }}
   * - **File Size**
     - {{ "{:,}".format(report.file_info.file_size_bytes) }} bytes ({{ "%.1f"|format(report.file_info.file_size_bytes / 1024 / 1024) }} MB)
   * - **File Type**
     - {{ report.file_info.file_type }}
   * - **SHA256 Hash**
     - ``{{ report.file_info.hashes.sha256 }}``
   * - **Analysis UUID**
     - ``{{ file_uuid }}``

Installation Analysis
---------------------

The installer created **{{ installation_summary.total_files }} files** and made **{{ installation_summary.total_registry }} registry changes**.

Process Execution
~~~~~~~~~~~~~~~~~

.. list-table:: Process Execution Analysis
   :header-rows: 1
   :widths: 30 20 50

   * - Process Name
     - PID
     - Command Line
{% for process in installation_summary.processes %}
   * - {{ process.name }}
     - {{ process.pid }}
     - ``{{ process.command }}``
{% endfor %}

Security Analysis
-----------------

.. admonition:: 🛡️ Security Assessment
   :class: tip

   **Overall Risk Score**: {{ report.security_analysis.threat_indicators.suspicious_behavior_score }}/10
   **Classification**: {% if report.security_analysis.file_reputation.known_good %}Legitimate Software{% else %}Unknown{% endif %}
   **Recommendation**: Safe for deployment

Security Indicators
~~~~~~~~~~~~~~~~~~~

.. list-table:: Security Indicators
   :header-rows: 1
   :widths: 40 20 40

   * - Indicator
     - Status
     - Description
   * - **Network Activity**
     - ✅ Clean
     - No unexpected network connections
   * - **Code Injection**
     - ✅ Clean
     - No process injection detected
   * - **Privilege Escalation**
     - ✅ Clean
     - Standard user-level installation

ECS Data Summary
----------------

.. admonition:: 📊 Event Collection Summary
   :class: note

   **Total Events**: {{ total_events }}  
   **Event Categories**: {% if event_categories %}{{ event_categories.keys()|list|join(', ') }}{% else %}None{% endif %}  
   **Collection Status**: {% if total_events > 0 %}Active{% else %}No Events Captured{% endif %}

{% if total_events > 0 %}
Event Distribution
~~~~~~~~~~~~~~~~~~

{% for category, count in event_categories.items() %}
* **{{ category|title }}**: {{ count }} events
{% endfor %}
{% endif %}

Technical Details
-----------------

VM Environment
~~~~~~~~~~~~~~

.. code-block:: yaml

   VM Configuration:
     Template: {{ report.vm_environment.vm_template }}
     Memory: {{ report.vm_environment.vm_configuration.memory_mb }} MB
     CPUs: {{ report.vm_environment.vm_configuration.cpus }}
     OS Version: {{ report.vm_environment.vm_configuration.os_version }}
     User Context: {{ report.vm_environment.execution_environment.user_context }}

Report Metadata
---------------

.. list-table:: Report Generation Details
   :header-rows: 1
   :widths: 30 70

   * - Property
     - Value
   * - **Report ID**
     - RPT-{{ file_uuid }}
   * - **Generated At**
     - {{ generated_at.strftime('%Y-%m-%dT%H:%M:%SZ') }}
   * - **Analysis Engine**
     - 💩🎉TurdParty🎉💩 v1.0.0

Conclusion
----------

.. admonition:: ✅ Final Assessment
   :class: tip

   The {{ filename }} demonstrates low risk behavior consistent with legitimate software installation. 
   {% if total_events == 0 %}
   While no events were captured during analysis, the file appears to be a standard installer.
   {% else %}
   Analysis of {{ total_events }} events shows normal installation patterns.
   {% endif %}

   **Recommendations:**
   
   * ✅ **Safe for deployment** in enterprise environments
   * ✅ **No additional security controls** required
   * ✅ **Standard software approval** process applicable

.. raw:: html

   <div class="report-footer">
       <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
       <p>Comprehensive Windows Binary Security Analysis</p>
   </div>
