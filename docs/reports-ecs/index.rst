💩🎉TurdParty🎉💩 ECS Analysis Reports
=============================================

**Generated on:** 2025-06-19 23:43:20 UTC

**Analysis Summary:**

- **Total Binaries Analyzed:** 10
- **Report Generation:** Sphinx Templates
- **Data Source:** ECS Comprehensive Reports

Binary Analysis Reports
-----------------------

.. toctree::
   :maxdepth: 2
   :caption: Individual Binary Reports

   reports/vscode-analysis
   reports/nodejs-analysis
   reports/python-analysis
   reports/chrome-analysis
   reports/firefox-analysis
   reports/git-analysis
   reports/notepadpp-analysis
   reports/7zip-analysis
   reports/putty-analysis
   reports/vlc-analysis


Summary Statistics
------------------

.. list-table:: Binary Analysis Summary
   :widths: 25 25 25 25
   :header-rows: 1

   * - Binary
     - File UUID
     - Total Events
     - Status
   * - Vscode
     - 2d8bde4c...
     - 0
     - ✅ Success
   * - Nodejs
     - a35065f9...
     - 0
     - ✅ Success
   * - Python
     - 63b0c18f...
     - 0
     - ✅ Success
   * - Chrome
     - 2a26b9f3...
     - 0
     - ✅ Success
   * - Firefox
     - 653579f8...
     - 0
     - ✅ Success
   * - Git
     - cae3bc4a...
     - 0
     - ✅ Success
   * - Notepadpp
     - 7023282c...
     - 0
     - ✅ Success
   * - 7Zip
     - bc5943fe...
     - 0
     - ✅ Success
   * - Putty
     - 74a7373d...
     - 0
     - ✅ Success
   * - Vlc
     - 2e674e94...
     - 0
     - ✅ Success


.. note::
   This documentation was automatically generated from ECS comprehensive reports
   using the TurdParty Sphinx template system.

