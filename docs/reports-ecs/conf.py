# Configuration file for the Sphinx documentation builder.
# TurdParty Binary Analysis Reports Platform

from datetime import datetime
import os
import sys

# -- Path setup --------------------------------------------------------------
sys.path.insert(0, os.path.abspath("../../"))

# -- Project information -----------------------------------------------------
project = "💩🎉 TurdParty Binary Analysis Reports"
copyright = f"{datetime.now().year}, TurdParty Security Research Team"
author = "TurdParty Analysis Engine"
release = "1.0.0"
version = "1.0"

# -- General configuration ---------------------------------------------------
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.viewcode",
    "sphinx.ext.napoleon",
    "sphinx.ext.intersphinx",
    "sphinx.ext.todo",
    "sphinx.ext.coverage",
    "sphinx.ext.ifconfig",
    "sphinx.ext.githubpages",
    "sphinx_rtd_theme",
]

# Add any paths that contain templates here, relative to this directory.
templates_path = ["_templates"]

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
exclude_patterns = ["_build", "Thumbs.db", ".DS_Store"]

# Suppress duplicate label warnings for section titles
suppress_warnings = ['ref.duplicate']

# -- Options for HTML output -------------------------------------------------
html_theme = "sphinx_rtd_theme"
html_theme_options = {
    "analytics_id": "",
    "analytics_anonymize_ip": False,
    "logo_only": False,
    "display_version": True,
    "prev_next_buttons_location": "bottom",
    "style_external_links": False,
    "vcs_pageview_mode": "",
    "style_nav_header_background": "#2c3e50",
    "collapse_navigation": False,
    "sticky_navigation": True,
    "navigation_depth": 4,
    "includehidden": True,
    "titles_only": False,
}

html_static_path = ["_static"]
html_css_files = [
    "custom.css",
]

html_js_files = [
    "custom.js",
]

# Custom sidebar
html_sidebars = {
    "**": [
        "globaltoc.html",
        "relations.html",
        "sourcelink.html",
        "searchbox.html",
    ]
}

# -- Options for LaTeX output ------------------------------------------------
latex_elements = {
    "papersize": "letterpaper",
    "pointsize": "10pt",
    "preamble": "",
    "fncychap": "",
    "printindex": "",
}

latex_documents = [
    (
        "index",
        "TurdPartyReports.tex",
        "TurdParty Binary Analysis Reports Documentation",
        "TurdParty Team",
        "manual",
    ),
]

# -- Options for manual page output ------------------------------------------
man_pages = [
    (
        "index",
        "turdpartyreports",
        "TurdParty Binary Analysis Reports Documentation",
        [author],
        1,
    )
]

# -- Options for Texinfo output ----------------------------------------------
texinfo_documents = [
    (
        "index",
        "TurdPartyReports",
        "TurdParty Binary Analysis Reports Documentation",
        author,
        "TurdPartyReports",
        "Comprehensive binary analysis and threat intelligence reports.",
        "Miscellaneous",
    ),
]

# -- Extension configuration -------------------------------------------------

# -- Options for intersphinx extension ---------------------------------------
intersphinx_mapping = {
    "python": ("https://docs.python.org/3", None),
    "sphinx": ("https://www.sphinx-doc.org/en/master/", None),
}

# -- Options for todo extension ----------------------------------------------
todo_include_todos = True

# -- Options for mermaid extension -------------------------------------------
mermaid_version = "10.6.1"
mermaid_init_js = """
mermaid.initialize({
    startOnLoad: true,
    theme: 'dark',
    themeVariables: {
        primaryColor: '#3498db',
        primaryTextColor: '#ecf0f1',
        primaryBorderColor: '#2c3e50',
        lineColor: '#95a5a6',
        sectionBkgColor: '#34495e',
        altSectionBkgColor: '#2c3e50',
        gridColor: '#7f8c8d',
        secondaryColor: '#e74c3c',
        tertiaryColor: '#f39c12'
    }
});
"""

# -- Custom configuration for TurdParty Reports -----------------------------

# Report generation settings
turdparty_report_settings = {
    "auto_generate_index": True,
    "include_threat_analysis": True,
    "include_behavioral_patterns": True,
    "include_installation_footprint": True,
    "include_runtime_analysis": True,
    "include_security_assessment": True,
    "generate_executive_summary": True,
    "include_technical_details": True,
    "export_formats": ["html", "pdf", "json"],
    "classification_levels": ["public", "internal", "confidential", "restricted"],
    "default_classification": "internal",
}

# Report template configuration
turdparty_templates = {
    "executive_summary": "templates/executive_summary.rst",
    "technical_analysis": "templates/technical_analysis.rst",
    "threat_assessment": "templates/threat_assessment.rst",
    "installation_footprint": "templates/installation_footprint.rst",
    "runtime_behavior": "templates/runtime_behavior.rst",
    "security_analysis": "templates/security_analysis.rst",
    "comparative_analysis": "templates/comparative_analysis.rst",
}

# API integration settings
turdparty_api_config = {
    "base_url": "http://api.turdparty.localhost/api/v1",
    "reports_endpoint": "/reports",
    "auto_refresh_interval": 300,  # 5 minutes
    "cache_reports": True,
    "cache_duration": 3600,  # 1 hour
}

# Dark mode support
html_theme_options.update(
    {
        "style_nav_header_background": "#1a1a1a",
    }
)

# Custom CSS for dark mode and TurdParty branding
html_css_files.extend(["turdparty-theme.css", "report-styles.css"])

# Report categorization
report_categories = {
    "malware_analysis": "Malware Analysis Reports",
    "software_analysis": "Software Analysis Reports",
    "vulnerability_assessment": "Vulnerability Assessment Reports",
    "threat_intelligence": "Threat Intelligence Reports",
    "comparative_analysis": "Comparative Analysis Reports",
    "executive_summaries": "Executive Summary Reports",
}

# Security classification colors
classification_colors = {
    "public": "#27ae60",  # Green
    "internal": "#f39c12",  # Orange
    "confidential": "#e74c3c",  # Red
    "restricted": "#8e44ad",  # Purple
}
