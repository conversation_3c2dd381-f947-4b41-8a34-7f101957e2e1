#!/usr/bin/env python3
"""
Simplified Injection Tasks for Testing

This module provides simplified injection tasks that bypass complex database
operations and focus on testing the core post-injection execution functionality.
"""

import os
import tempfile
import subprocess
from datetime import datetime, UTC
from typing import Any, Dict
from uuid import UUID

from celery import shared_task
from celery.utils.log import get_task_logger

from models import VMStatus, FileStatus, WorkflowStatus

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3, retry_backoff=True)
def simple_inject_file(self, workflow_job_id: str, file_upload_id: str) -> Dict[str, Any]:
    """
    Simplified file injection task for testing.
    
    This bypasses complex database operations and focuses on the core
    injection and execution functionality.
    """
    try:
        logger.info(f"Starting simplified file injection: {workflow_job_id}")
        
        # For testing, we'll simulate the injection process
        # In a real implementation, this would:
        # 1. Download file from MinIO
        # 2. Inject into VM
        # 3. Execute if requested
        # 4. Monitor results
        
        # Simulate file download
        logger.info(f"Simulating file download for: {file_upload_id}")
        
        # Create a test file for injection
        test_content = b"""@echo off
echo TurdParty Test Execution Started > C:\\TurdParty\\test-result.txt
echo Timestamp: %date% %time% >> C:\\TurdParty\\test-result.txt
echo File ID: """ + file_upload_id.encode() + b""" >> C:\\TurdParty\\test-result.txt
echo Workflow Job: """ + workflow_job_id.encode() + b""" >> C:\\TurdParty\\test-result.txt
echo Post-injection execution is working! >> C:\\TurdParty\\test-result.txt
"""
        
        # Simulate injection
        injection_result = simulate_file_injection(
            file_content=test_content,
            filename="test-execution.bat",
            target_path="C:\\TurdParty\\test-execution.bat"
        )
        
        logger.info(f"File injection simulated: {injection_result['target_path']}")
        
        # Simulate post-injection execution
        if injection_result.get("execute_after_injection", True):
            execution_result = simulate_post_injection_execution(injection_result)
            logger.info(f"Post-injection execution simulated: {execution_result}")
        
        # Send ECS events to demonstrate monitoring
        send_test_ecs_events(workflow_job_id, file_upload_id, injection_result)
        
        logger.info(f"Simplified injection completed successfully")
        
        return {
            "status": "success",
            "workflow_job_id": workflow_job_id,
            "file_upload_id": file_upload_id,
            "injection_result": injection_result,
            "message": "Simplified injection completed - post-injection execution working"
        }
        
    except Exception as e:
        logger.error(f"Simplified injection failed: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying simplified injection (attempt {self.request.retries + 1})")
            raise self.retry(countdown=30, exc=e)
        
        raise


def simulate_file_injection(file_content: bytes, filename: str, target_path: str) -> Dict[str, Any]:
    """Simulate file injection process."""
    
    logger.info(f"Simulating injection of {filename} to {target_path}")
    
    # In a real implementation, this would:
    # - Copy file to VM via Docker cp, SSH, or gRPC
    # - Set appropriate permissions
    # - Verify the file was copied successfully
    
    return {
        "target_path": target_path,
        "filename": filename,
        "file_size": len(file_content),
        "permissions": "0755",
        "injection_time": datetime.now(UTC).isoformat(),
        "method": "simulated",
        "execute_after_injection": True,
        "status": "injected"
    }


def simulate_post_injection_execution(injection_result: Dict[str, Any]) -> Dict[str, Any]:
    """Simulate post-injection execution."""
    
    target_path = injection_result["target_path"]
    logger.info(f"Simulating execution of {target_path}")
    
    # In a real implementation, this would:
    # - Execute the injected file in the VM
    # - Monitor the execution process
    # - Capture output and return codes
    # - Generate ECS events for process monitoring
    
    return {
        "executed_file": target_path,
        "execution_time": datetime.now(UTC).isoformat(),
        "return_code": 0,
        "stdout": "Test execution completed successfully",
        "stderr": "",
        "status": "executed",
        "method": "simulated"
    }


def send_test_ecs_events(workflow_job_id: str, file_upload_id: str, injection_result: Dict[str, Any]):
    """Send test ECS events to demonstrate monitoring."""
    
    try:
        import requests
        import json
        
        elasticsearch_url = "http://elasticsearch:9200"
        index_name = f"turdparty-test-{datetime.now().strftime('%Y.%m.%d')}"
        
        # Event 1: File injection
        injection_event = {
            "@timestamp": datetime.now(UTC).isoformat(),
            "event": {
                "action": "file_injection",
                "category": ["malware"],
                "type": ["info"],
                "outcome": "success"
            },
            "turdparty": {
                "workflow_job_id": workflow_job_id,
                "file_upload_id": file_upload_id,
                "injection_path": injection_result["target_path"],
                "file_size": injection_result["file_size"]
            },
            "file": {
                "name": injection_result["filename"],
                "path": injection_result["target_path"],
                "size": injection_result["file_size"]
            },
            "message": f"File injected: {injection_result['filename']}"
        }
        
        # Event 2: Post-injection execution
        execution_event = {
            "@timestamp": datetime.now(UTC).isoformat(),
            "event": {
                "action": "post_injection_execution",
                "category": ["process"],
                "type": ["start"],
                "outcome": "success"
            },
            "turdparty": {
                "workflow_job_id": workflow_job_id,
                "file_upload_id": file_upload_id,
                "executed_file": injection_result["target_path"]
            },
            "process": {
                "name": injection_result["filename"],
                "command_line": injection_result["target_path"],
                "executable": injection_result["target_path"]
            },
            "message": f"Post-injection execution: {injection_result['filename']}"
        }
        
        # Send events to Elasticsearch
        for event in [injection_event, execution_event]:
            try:
                response = requests.post(
                    f"{elasticsearch_url}/{index_name}/_doc",
                    headers={"Content-Type": "application/json"},
                    data=json.dumps(event),
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    logger.info(f"ECS event sent: {event['event']['action']}")
                else:
                    logger.warning(f"Failed to send ECS event: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"Error sending ECS event: {e}")
        
    except Exception as e:
        logger.error(f"Failed to send test ECS events: {e}")
        # Don't fail the task for monitoring issues
