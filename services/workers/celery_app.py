"""Celery application for TurdParty workers."""

import os

from celery import Celery
from celery.schedules import crontab

# Import Elasticsearch logging
try:
    from elasticsearch_logging import setup_elasticsearch_logging
except ImportError:
    # Fallback if import fails
    def setup_elasticsearch_logging(url):
        print("⚠️ Elasticsearch logging not available, using console logging")


# Configuration
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

BROKER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB + 1}"

# Create Celery app
app = Celery(
    "turdparty_workers",
    broker=BROKER_URL,
    backend=RESULT_BACKEND,
    include=[
        "tasks.file_operations",
        "tasks.vm_management",
        "tasks.injection_tasks",  # Re-enabled for file injection testing
        "tasks.simple_injection_tasks",  # Simplified injection tasks for testing
        "tasks.vm_pool_manager",
        "services.workers.tasks.enhanced_vm_pool_manager",
        "services.workers.tasks.basic_vm_availability_manager",
        "tasks.workflow_orchestrator",
        "tasks.elk_integration",
        "tasks.vm_agent_injector",
        "tasks.scheduled_maintenance",
        "tasks.task_monitoring",
        "tasks.simple_file_ops",
        "tasks.simple_vm_ops",
        "tasks.simple_elk_ops",
        "tasks.simple_workflow",
    ],
)

# Configuration
app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    task_routes={
        "tasks.file_operations.*": {"queue": "file_ops"},
        "tasks.vm_management.*": {"queue": "vm_ops"},
        "tasks.injection_tasks.*": {"queue": "injection_ops"},
        "tasks.simple_injection_tasks.*": {"queue": "injection_ops"},
        "tasks.vm_pool_manager.*": {"queue": "pool_ops"},
        "services.workers.tasks.enhanced_vm_pool_manager.*": {"queue": "pool_ops"},
        "services.workers.tasks.basic_vm_availability_manager.*": {"queue": "pool_ops"},
        "tasks.workflow_orchestrator.*": {"queue": "workflow_ops"},
        "tasks.elk_integration.*": {"queue": "elk_ops"},
        "tasks.vm_agent_injector.*": {"queue": "injection_ops"},
        "tasks.scheduled_maintenance.*": {"queue": "maintenance"},
        "tasks.task_monitoring.*": {"queue": "monitoring"},
    },
    task_default_queue="default",
    task_default_exchange="default",
    task_default_routing_key="default",
    beat_schedule={
        # VM Pool Health Check - Every 5 minutes
        "vm-pool-health-check": {
            "task": "tasks.scheduled_maintenance.vm_pool_health_check",
            "schedule": 300.0,  # 5 minutes
            "options": {"queue": "maintenance"},
        },
        # Enhanced VM Pool Maintenance - Every 3 minutes (Temporarily disabled)
        # 'enhanced-pool-maintenance': {
        #     'task': 'services.workers.tasks.enhanced_vm_pool_manager.maintain_enhanced_pools',
        #     'schedule': 180.0,  # 3 minutes
        #     'options': {'queue': 'pool_ops'}
        # },
        # Enhanced VM Pool Health Check - Every 10 minutes (Temporarily disabled)
        # 'enhanced-pool-health-check': {
        #     'task': 'services.workers.tasks.enhanced_vm_pool_manager.health_check_enhanced_pools',
        #     'schedule': 600.0,  # 10 minutes
        #     'options': {'queue': 'pool_ops'}
        # },
        # Resource Cleanup - Every hour
        "cleanup-orphaned-resources": {
            "task": "tasks.scheduled_maintenance.cleanup_orphaned_resources",
            "schedule": 3600.0,  # 1 hour
            "options": {"queue": "maintenance"},
        },
        # ELK Index Management - Daily at 2 AM UTC
        "elk-index-management": {
            "task": "tasks.scheduled_maintenance.elk_index_management",
            "schedule": crontab(hour=2, minute=0),
            "options": {"queue": "maintenance"},
        },
        # Cachet Integration - Every 10 minutes
        "update-cachet-metrics": {
            "task": "tasks.scheduled_maintenance.update_cachet_metrics",
            "schedule": 600.0,  # 10 minutes
            "options": {"queue": "maintenance"},
        },
        # Celery Worker Monitoring - Every 15 minutes
        "monitor-celery-workers": {
            "task": "tasks.task_monitoring.monitor_celery_workers",
            "schedule": 900.0,  # 15 minutes
            "options": {"queue": "monitoring"},
        },
        # Task Metrics Collection - Every 10 minutes
        "collect-task-metrics": {
            "task": "tasks.task_monitoring.collect_task_metrics",
            "schedule": 600.0,  # 10 minutes
            "options": {"queue": "monitoring"},
        },
        # Legacy tasks (keeping for compatibility)
        "maintain-vm-pool": {
            "task": "tasks.vm_pool_manager.maintain_pool",
            "schedule": 300.0,  # 5 minutes
            "options": {"queue": "pool_ops"},
        },
        "cleanup-terminated-vms": {
            "task": "tasks.vm_pool_manager.cleanup_terminated_vms",
            "schedule": 600.0,  # 10 minutes
            "options": {"queue": "pool_ops"},
        },
        "check-elk-health": {
            "task": "tasks.elk_integration.check_elk_health",
            "schedule": 120.0,  # 2 minutes
            "options": {"queue": "elk_ops"},
        },
    },
)

# Setup Elasticsearch logging for all workers
ELASTICSEARCH_URL = os.getenv("ELASTICSEARCH_URL", "http://elasticsearch:9200")
setup_elasticsearch_logging(ELASTICSEARCH_URL)

# Explicitly import simple injection tasks to ensure registration
try:
    import tasks.simple_injection_tasks
    print("✅ Simple injection tasks imported successfully")
except ImportError as e:
    print(f"⚠️ Failed to import simple injection tasks: {e}")

if __name__ == "__main__":
    app.start()
