"""
Virtual Machine Management API Endpoints

This module provides comprehensive REST API endpoints for managing virtual machines
in the TurdParty malware analysis platform. It supports both Vagrant and Docker
VM types with full lifecycle management capabilities.

Key Features:
    - VM creation with template selection and resource configuration
    - Complete lifecycle management (start, stop, restart, destroy, suspend, resume)
    - Real-time WebSocket connections for monitoring and command execution
    - Template management with compatibility validation
    - Resource allocation and constraint enforcement
    - Background task orchestration via Celery
    - Comprehensive error handling and validation

VM Types Supported:
    - **Docker Containers**: Lightweight, fast provisioning for Linux environments
    - **Vagrant VMs**: Full virtualization for Windows and complex Linux setups

Template Categories:
    - Ubuntu variants (18.04, 20.04, 22.04 LTS)
    - Debian stable releases
    - CentOS enterprise distributions
    - Alpine minimal distributions
    - Custom templates for specialized requirements

API Endpoints:
    - GET /vms/templates - List available VM templates
    - POST /vms/ - Create new VM instance
    - GET /vms/ - List all VMs with filtering
    - GET /vms/{id} - Get detailed VM information
    - POST /vms/{id}/action - Perform VM lifecycle actions
    - DELETE /vms/{id} - Delete VM and cleanup resources
    - WebSocket /vms/{id}/metrics/stream - Real-time metrics
    - WebSocket /vms/{id}/commands/execute - Command execution

Security Features:
    - Domain enforcement (TurdParty domain required)
    - Resource limits and validation
    - Automatic VM termination (30-minute limit)
    - Isolated network environments
    - Comprehensive audit logging

Integration:
    - Celery workers for background VM operations
    - Database persistence for VM state tracking
    - ELK stack integration for monitoring
    - WebSocket connections for real-time communication
"""

import asyncio
from datetime import UTC, datetime
from enum import Enum
import json
import logging
from typing import Any
import uuid
from uuid import UUID

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    HTTPException,
    Query,
    WebSocket,
    WebSocketDisconnect,
)
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ...models.vm_instance import VMInstance, VMStatus
from ...services.celery_app import get_celery_app
from ...services.database import get_db

logger = logging.getLogger(__name__)
router = APIRouter()


# WebSocket connection manager
class ConnectionManager:
    """
    Manages WebSocket connections for VM real-time communication.

    Handles multiple WebSocket connections per VM for real-time metrics,
    command execution, and status updates. Provides connection lifecycle
    management and message broadcasting capabilities.
    """

    def __init__(self):
        """Initialize connection manager with empty connections dictionary."""
        self.active_connections: dict[str, list[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, vm_id: str):
        """
        Accept and register a new WebSocket connection for a VM.

        Args:
            websocket: The WebSocket connection to register
            vm_id: Unique identifier of the VM this connection is for
        """
        await websocket.accept()
        if vm_id not in self.active_connections:
            self.active_connections[vm_id] = []
        self.active_connections[vm_id].append(websocket)
        logger.info(f"WebSocket connected for VM {vm_id}")

    def disconnect(self, websocket: WebSocket, vm_id: str):
        """
        Remove a WebSocket connection from the VM's connection list.

        Args:
            websocket: The WebSocket connection to remove
            vm_id: Unique identifier of the VM this connection was for
        """
        if vm_id in self.active_connections:
            self.active_connections[vm_id].remove(websocket)
            if not self.active_connections[vm_id]:
                del self.active_connections[vm_id]
        logger.info(f"WebSocket disconnected for VM {vm_id}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """
        Send a message to a specific WebSocket connection.

        Args:
            message: Text message to send
            websocket: Target WebSocket connection
        """
        await websocket.send_text(message)

    async def broadcast_to_vm(self, message: str, vm_id: str):
        """
        Broadcast a message to all WebSocket connections for a specific VM.

        Args:
            message: Text message to broadcast
            vm_id: Unique identifier of the VM whose connections should receive the message

        Note:
            Failed connections are logged but don't interrupt the broadcast to other connections.
        """
        if vm_id in self.active_connections:
            for connection in self.active_connections[vm_id]:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to VM {vm_id}: {e}")


manager = ConnectionManager()


@router.get("/templates")
async def get_vm_templates() -> list[dict[str, Any]]:
    """
    Get all available VM templates.

    Returns templates with descriptions and compatibility information.
    """
    templates = []

    template_descriptions = {
        "UBUNTU_2004": "Ubuntu 20.04 LTS (Focal Fossa) - Recommended for most workloads",
        "UBUNTU_2204": "Ubuntu 22.04 LTS (Jammy Jellyfish) - Latest LTS with modern packages",
        "UBUNTU_1804": "Ubuntu 18.04 LTS (Bionic Beaver) - Legacy support",
        "DEBIAN_11": "Debian 11 (Bullseye) - Stable and secure",
        "CENTOS_7": "CentOS 7 - Enterprise Linux compatible",
        "CENTOS_8": "CentOS 8 - Modern enterprise features",
        "ALPINE": "Alpine Linux - Minimal and secure",
        "DOCKER_UBUNTU_2004": "Docker Ubuntu 20.04 - Containerized Ubuntu",
        "DOCKER_UBUNTU_2204": "Docker Ubuntu 22.04 - Latest containerized Ubuntu",
        "DOCKER_ALPINE": "Docker Alpine - Minimal container",
        "CUSTOM": "Custom template - Bring your own configuration",
    }

    vm_type_compatibility = {
        "UBUNTU_2004": ["vagrant", "docker"],
        "UBUNTU_2204": ["vagrant", "docker"],
        "UBUNTU_1804": ["vagrant", "docker"],
        "DEBIAN_11": ["vagrant", "docker"],
        "CENTOS_7": ["vagrant", "docker"],
        "CENTOS_8": ["vagrant", "docker"],
        "ALPINE": ["vagrant", "docker"],
        "DOCKER_UBUNTU_2004": ["docker"],
        "DOCKER_UBUNTU_2204": ["docker"],
        "DOCKER_ALPINE": ["docker"],
        "CUSTOM": ["vagrant", "docker"],
    }

    template_values = {
        "UBUNTU_2004": "ubuntu/focal64",
        "UBUNTU_2204": "ubuntu/jammy64",
        "UBUNTU_1804": "ubuntu/bionic64",
        "DEBIAN_11": "debian/bullseye64",
        "CENTOS_7": "centos/7",
        "CENTOS_8": "centos/8",
        "ALPINE": "alpine/alpine64",
        "DOCKER_UBUNTU_2004": "ubuntu:20.04",
        "DOCKER_UBUNTU_2204": "ubuntu:22.04",
        "DOCKER_ALPINE": "alpine:latest",
        "CUSTOM": "custom",
    }

    for template_name in template_descriptions:
        template_info = {
            "value": template_values.get(template_name, template_name.lower()),
            "name": template_name,
            "description": template_descriptions.get(
                template_name, "No description available"
            ),
            "compatible_vm_types": vm_type_compatibility.get(
                template_name, ["vagrant", "docker"]
            ),
            "recommended": template_name in ["UBUNTU_2004", "DOCKER_UBUNTU_2004"],
        }
        templates.append(template_info)

    return templates


class VMTemplate(str, Enum):
    """VM template enum based on reference repository patterns."""

    UBUNTU_2004 = "ubuntu/focal64"
    UBUNTU_2204 = "ubuntu/jammy64"
    UBUNTU_1804 = "ubuntu/bionic64"
    DEBIAN_11 = "debian/bullseye64"
    CENTOS_7 = "centos/7"
    CENTOS_8 = "centos/8"
    ALPINE = "alpine/alpine64"
    DOCKER_UBUNTU_2004 = "ubuntu:20.04"
    DOCKER_UBUNTU_2204 = "ubuntu:22.04"
    DOCKER_ALPINE = "alpine:latest"
    WINDOWS_10_10BAHT = "10Baht/windows10-turdparty"  # 10Baht Packer built Windows 10
    CUSTOM = "custom"


class VMAction(str, Enum):
    """VM action enum for operations."""

    START = "start"
    STOP = "stop"
    RESTART = "restart"
    DESTROY = "destroy"
    SUSPEND = "suspend"
    RESUME = "resume"


class VMActionRequest(BaseModel):
    """VM action request schema."""

    action: VMAction
    force: bool = Field(default=False, description="Force the action")


class VMCreateRequest(BaseModel):
    """VM creation request schema."""

    name: str = Field(..., description="VM name (must be unique)")
    template: VMTemplate = Field(
        default=VMTemplate.UBUNTU_2004, description="VM template"
    )
    vm_type: str = Field(default="docker", description="VM type (docker or vagrant)")
    memory_mb: int = Field(default=1024, ge=256, le=8192, description="Memory in MB")
    cpus: int = Field(default=1, ge=1, le=8, description="Number of CPU cores")
    disk_gb: int = Field(default=20, ge=5, le=100, description="Disk size in GB")
    domain: str = Field(default="TurdParty", description="VM domain")
    description: str | None = Field(None, description="VM description")
    auto_start: bool = Field(default=True, description="Auto-start VM after creation")
    provision_script: str | None = Field(None, description="Custom provision script")


class VMResponse(BaseModel):
    """VM response schema."""

    vm_id: str
    name: str
    template: str
    vm_type: str
    memory_mb: int
    cpus: int
    disk_gb: int
    status: str
    domain: str
    ip_address: str | None = None
    ssh_port: int | None = None
    runtime_minutes: float
    is_expired: bool
    created_at: str
    started_at: str | None = None
    terminated_at: str | None = None
    description: str | None = None
    error_message: str | None = None


@router.post("/", response_model=VMResponse, status_code=201)
async def create_vm(
    vm_request: VMCreateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
) -> VMResponse:
    """
    Create a new VM instance (Vagrant or Docker).

    Enforces TurdParty domain for all VMs and validates template compatibility.
    """
    try:
        # Enforce TurdParty domain
        if vm_request.domain != "TurdParty":
            raise HTTPException(
                status_code=400, detail="All VMs must be created with TurdParty domain"
            )

        # Check if VM name already exists
        result = await db.execute(
            select(VMInstance).where(VMInstance.name == vm_request.name)
        )
        existing_vm = result.scalar_one_or_none()

        if existing_vm:
            raise HTTPException(
                status_code=400,
                detail=f"VM with name '{vm_request.name}' already exists",
            )

        # Validate template compatibility with VM type
        if vm_request.vm_type == "docker" and not vm_request.template.value.startswith(
            ("ubuntu:", "alpine:", "centos:", "debian:")
        ):
            # Convert Vagrant template to Docker equivalent
            docker_templates = {
                VMTemplate.UBUNTU_2004: "ubuntu:20.04",
                VMTemplate.UBUNTU_2204: "ubuntu:22.04",
                VMTemplate.UBUNTU_1804: "ubuntu:18.04",
                VMTemplate.DEBIAN_11: "debian:bullseye",
                VMTemplate.CENTOS_7: "centos:7",
                VMTemplate.CENTOS_8: "centos:8",
                VMTemplate.ALPINE: "alpine:latest",
            }
            template_value = docker_templates.get(
                vm_request.template, vm_request.template.value
            )
        else:
            template_value = vm_request.template.value

        # Create VM instance record
        vm_instance = VMInstance(
            name=vm_request.name,
            template=template_value,
            memory_mb=vm_request.memory_mb,
            cpus=vm_request.cpus,
            disk_gb=vm_request.disk_gb,
            status=VMStatus.CREATING,
        )

        db.add(vm_instance)
        await db.commit()
        await db.refresh(vm_instance)

        # Queue VM creation task
        celery_app = get_celery_app()
        task = celery_app.send_task(
            "services.workers.tasks.vm_management.create_vm",
            args=[
                str(vm_instance.id),
                vm_request.vm_type,
                {
                    "name": vm_request.name,
                    "template": template_value,
                    "memory_mb": vm_request.memory_mb,
                    "cpus": vm_request.cpus,
                    "disk_gb": vm_request.disk_gb,
                    "provision_script": vm_request.provision_script,
                },
            ],
            queue="vm_ops",
        )

        logger.info(f"VM creation queued: {vm_instance.id} (task: {task.id})")

        return VMResponse(
            vm_id=str(vm_instance.id),
            name=vm_instance.name,
            template=vm_instance.template,
            vm_type=vm_request.vm_type,
            memory_mb=vm_instance.memory_mb,
            cpus=vm_instance.cpus,
            disk_gb=vm_instance.disk_gb,
            status=vm_instance.status.value,
            domain=vm_request.domain,
            runtime_minutes=vm_instance.runtime_minutes,
            is_expired=vm_instance.is_expired,
            created_at=vm_instance.created_at.isoformat(),
            description=vm_request.description,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create VM: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"VM creation failed: {e!s}")


@router.get("/")
async def list_vms(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: VMStatus | None = Query(None),
    db: AsyncSession = Depends(get_db),
) -> dict[str, Any]:
    """List all VM instances with optional filtering."""
    try:
        query = select(VMInstance)

        if status:
            query = query.where(VMInstance.status == status)

        query = query.offset(skip).limit(limit).order_by(VMInstance.created_at.desc())

        result = await db.execute(query)
        vms = result.scalars().all()

        return {
            "vms": [
                {
                    "vm_id": str(vm.id),
                    "name": vm.name,
                    "template": vm.template,
                    "status": vm.status.value,
                    "memory_mb": vm.memory_mb,
                    "cpus": vm.cpus,
                    "ip_address": vm.ip_address,
                    "runtime_minutes": vm.runtime_minutes,
                    "created_at": vm.created_at.isoformat(),
                    "started_at": vm.started_at.isoformat() if vm.started_at else None,
                }
                for vm in vms
            ],
            "total": len(vms),
            "skip": skip,
            "limit": limit,
        }

    except Exception as e:
        logger.error(f"Failed to list VMs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve VMs")


@router.get("/{vm_id}")
async def get_vm_details(
    vm_id: UUID, db: AsyncSession = Depends(get_db)
) -> dict[str, Any]:
    """Get detailed information about a specific VM."""
    try:
        result = await db.execute(select(VMInstance).where(VMInstance.id == vm_id))
        vm = result.scalar_one_or_none()

        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")

        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "vm_id_external": vm.vm_id,
            "template": vm.template,
            "memory_mb": vm.memory_mb,
            "cpus": vm.cpus,
            "disk_gb": vm.disk_gb,
            "status": vm.status.value,
            "ip_address": vm.ip_address,
            "ssh_port": vm.ssh_port,
            "started_at": vm.started_at.isoformat() if vm.started_at else None,
            "scheduled_termination": vm.scheduled_termination.isoformat()
            if vm.scheduled_termination
            else None,
            "terminated_at": vm.terminated_at.isoformat() if vm.terminated_at else None,
            "runtime_minutes": vm.runtime_minutes,
            "is_expired": vm.is_expired,
            "injection_completed": vm.injection_completed,
            "monitoring_active": vm.monitoring_active,
            "elk_index": vm.elk_index,
            "error_message": vm.error_message,
            "workflow_job_id": str(vm.workflow_job_id) if vm.workflow_job_id else None,
            "created_at": vm.created_at.isoformat(),
            "updated_at": vm.updated_at.isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get VM details: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve VM details")


@router.post("/{vm_id}/action")
async def perform_vm_action(
    vm_id: UUID,
    action_request: VMActionRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
) -> dict[str, Any]:
    """
    Perform an action on a VM instance.

    Supports: start, stop, restart, destroy, suspend, resume
    """
    try:
        result = await db.execute(select(VMInstance).where(VMInstance.id == vm_id))
        vm = result.scalar_one_or_none()

        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")

        # Validate action based on current status
        if action_request.action == VMAction.START and vm.status == VMStatus.RUNNING:
            return {
                "vm_id": str(vm.id),
                "name": vm.name,
                "action": action_request.action.value,
                "status": vm.status.value,
                "message": "VM is already running",
            }

        if action_request.action == VMAction.STOP and vm.status == VMStatus.TERMINATED:
            return {
                "vm_id": str(vm.id),
                "name": vm.name,
                "action": action_request.action.value,
                "status": vm.status.value,
                "message": "VM is already terminated",
            }

        # Map actions to task names
        task_mapping = {
            VMAction.START: "services.workers.tasks.vm_management.start_vm",
            VMAction.STOP: "services.workers.tasks.vm_management.stop_vm",
            VMAction.RESTART: "services.workers.tasks.vm_management.restart_vm",
            VMAction.DESTROY: "services.workers.tasks.vm_management.delete_vm",
            VMAction.SUSPEND: "services.workers.tasks.vm_management.suspend_vm",
            VMAction.RESUME: "services.workers.tasks.vm_management.resume_vm",
        }

        task_name = task_mapping.get(action_request.action)
        if not task_name:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported action: {action_request.action.value}",
            )

        # Queue the appropriate task
        celery_app = get_celery_app()

        if action_request.action in [VMAction.STOP, VMAction.DESTROY]:
            task = celery_app.send_task(
                task_name, args=[str(vm.id), action_request.force], queue="vm_ops"
            )
        else:
            task = celery_app.send_task(task_name, args=[str(vm.id)], queue="vm_ops")

        # Update VM status based on action
        status_mapping = {
            VMAction.START: VMStatus.RUNNING,
            VMAction.STOP: VMStatus.TERMINATING,
            VMAction.RESTART: VMStatus.RUNNING,
            VMAction.DESTROY: VMStatus.TERMINATING,
            VMAction.SUSPEND: VMStatus.TERMINATING,
            VMAction.RESUME: VMStatus.RUNNING,
        }

        if action_request.action in status_mapping:
            vm.status = status_mapping[action_request.action]
            await db.commit()

        logger.info(
            f"VM {action_request.action.value} queued: {vm.id} (task: {task.id})"
        )

        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "action": action_request.action.value,
            "status": vm.status.value,
            "task_id": task.id,
            "force": action_request.force,
            "message": f"VM {action_request.action.value} queued",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to perform VM action: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"VM action failed: {e!s}")


@router.delete("/{vm_id}")
async def delete_vm(
    vm_id: UUID, force: bool = Query(False), db: AsyncSession = Depends(get_db)
) -> dict[str, Any]:
    """Delete a VM instance and clean up resources."""
    try:
        result = await db.execute(select(VMInstance).where(VMInstance.id == vm_id))
        vm = result.scalar_one_or_none()

        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")

        # Queue VM deletion task
        celery_app = get_celery_app()
        task = celery_app.send_task(
            "services.workers.tasks.vm_management.delete_vm",
            args=[str(vm.id), force],
            queue="vm_ops",
        )

        logger.info(f"VM deletion queued: {vm.id} (task: {task.id})")

        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "task_id": task.id,
            "force": force,
            "message": "VM deletion queued",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete VM: {e}")
        raise HTTPException(status_code=500, detail=f"VM deletion failed: {e!s}")


@router.post("/{vm_id}/inject")
async def inject_file_into_vm(
    vm_id: UUID, injection_data: dict, db: AsyncSession = Depends(get_db)
) -> dict[str, Any]:
    """Inject a file into a VM and execute it with real monitoring."""
    try:
        # Get VM instance
        result = await db.execute(select(VMInstance).where(VMInstance.id == vm_id))
        vm = result.scalar_one_or_none()

        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")

        if vm.status not in [VMStatus.RUNNING, VMStatus.MONITORING]:
            raise HTTPException(status_code=400, detail="VM is not in a ready state for injection")

        # Get file_id from injection_data
        file_id = injection_data.get("file_id")
        if not file_id:
            raise HTTPException(status_code=400, detail="file_id required")

        # Create workflow job for this injection
        from services.api.src.models.file_upload import FileUpload
        from services.api.src.models.workflow_job import WorkflowJob, WorkflowStatus

        # Get file upload record
        file_result = await db.execute(
            select(FileUpload).where(FileUpload.id == UUID(file_id))
        )
        file_upload = file_result.scalar_one_or_none()

        if not file_upload:
            raise HTTPException(status_code=404, detail="File not found")

        # Create workflow job
        workflow_job = WorkflowJob(
            name=f"File injection for VM {vm.name}",
            description=f"Inject {file_upload.filename} into VM {vm.name}",
            vm_instance_id=vm_id,
            file_upload_id=UUID(file_id),
            status=WorkflowStatus.PENDING,
            injection_config={
                "target_path": injection_data.get(
                    "injection_path", f"/tmp/{file_upload.filename}"
                ),
                "permissions": injection_data.get("permissions", "0755"),
                "execute_after_injection": injection_data.get(
                    "execute_after_injection", True
                ),
            },
        )

        db.add(workflow_job)
        await db.commit()
        await db.refresh(workflow_job)

        # Queue the simplified injection task for testing
        celery_app = get_celery_app()
        task = celery_app.send_task(
            "tasks.simple_injection_tasks.simple_inject_file",
            args=[str(workflow_job.id), file_id],
            queue="injection_ops",
        )

        logger.info(f"Real file injection queued for VM {vm_id}: task {task.id}")

        return {
            "vm_id": str(vm_id),
            "file_id": file_id,
            "workflow_job_id": str(workflow_job.id),
            "task_id": task.id,
            "injection_id": str(workflow_job.id),  # For test compatibility
            "success": True,
            "message": "Real file injection queued - monitoring will start automatically",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to inject file into VM: {e}")
        raise HTTPException(status_code=500, detail=f"File injection failed: {e!s}")


@router.get("/{vm_id}/injections/{injection_id}")
async def get_injection_status(
    vm_id: UUID, injection_id: str, db: AsyncSession = Depends(get_db)
) -> dict[str, Any]:
    """Get the status of a file injection."""
    try:
        from services.api.src.models.workflow_job import WorkflowJob

        # Get workflow job (injection)
        result = await db.execute(
            select(WorkflowJob).where(WorkflowJob.id == UUID(injection_id))
        )
        workflow_job = result.scalar_one_or_none()

        if not workflow_job:
            raise HTTPException(status_code=404, detail="Injection not found")

        # Get VM to verify ownership
        vm_result = await db.execute(select(VMInstance).where(VMInstance.id == vm_id))
        vm = vm_result.scalar_one_or_none()

        if not vm or workflow_job.vm_instance_id != vm_id:
            raise HTTPException(
                status_code=404, detail="Injection not found for this VM"
            )

        return {
            "injection_id": injection_id,
            "vm_id": str(vm_id),
            "status": workflow_job.status.value,
            "progress_percentage": workflow_job.progress_percentage,
            "current_step": workflow_job.current_step,
            "results": workflow_job.results or {},
            "error_message": workflow_job.error_message,
            "created_at": workflow_job.created_at.isoformat(),
            "updated_at": workflow_job.updated_at.isoformat(),
            "injection_completed": vm.injection_completed,
            "monitoring_active": vm.monitoring_active,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get injection status: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get injection status: {e!s}"
        )


# REST endpoints for VM metrics
@router.get("/{vm_id}/metrics")
async def get_vm_metrics(vm_id: str, vm_type: str = "docker"):
    """Get current VM metrics"""
    try:
        from ...services.vm_metrics_service import vm_metrics_service

        # Initialize metrics service if needed
        await vm_metrics_service.initialize()

        # Get current metrics
        metrics = await vm_metrics_service.get_vm_metrics(vm_id, vm_type)

        return {"success": True, "data": metrics}

    except Exception as e:
        logger.error(f"Error getting VM metrics for {vm_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get VM metrics: {e!s}")


# WebSocket endpoints
@router.websocket("/{vm_id}/metrics/stream")
async def stream_vm_metrics(websocket: WebSocket, vm_id: str, vm_type: str = "docker"):
    """Stream real-time VM metrics via WebSocket"""
    connection_id = str(uuid.uuid4())
    client_ip = websocket.client.host if websocket.client else "unknown"
    user_agent = websocket.headers.get("user-agent", "unknown")

    await manager.connect(websocket, vm_id)

    try:
        # Send initial connection confirmation
        await websocket.send_json(
            {
                "type": "connection_established",
                "vm_id": vm_id,
                "connection_id": connection_id,
                "timestamp": datetime.now(UTC).isoformat(),
            }
        )

        # Real metrics streaming using VM metrics service
        from ...services.vm_metrics_service import vm_metrics_service

        try:
            # Initialize metrics service
            await vm_metrics_service.initialize()

            # Stream real metrics
            async for metrics_data in vm_metrics_service.stream_vm_metrics(
                vm_id, vm_type, interval=1.0
            ):
                # Format metrics for WebSocket
                formatted_metrics = {
                    "type": "metrics_data",
                    "vm_id": vm_id,
                    "timestamp": datetime.now(UTC).isoformat(),
                    "data": {
                        "cpu_percent": metrics_data.get("cpu_percent", 0),
                        "memory_percent": metrics_data.get("memory_percent", 0),
                        "memory_used_mb": metrics_data.get("memory_used_mb", 0),
                        "memory_limit_mb": round(
                            metrics_data.get("memory_limit_bytes", 0) / (1024 * 1024), 2
                        ),
                        "network_io": {
                            "bytes_sent": metrics_data.get("network_tx_bytes", 0),
                            "bytes_recv": metrics_data.get("network_rx_bytes", 0),
                        },
                        "top_processes": metrics_data.get("top_processes", []),
                        "uptime_seconds": metrics_data.get("uptime_seconds", 0),
                        "status": metrics_data.get("status", "unknown"),
                    },
                }

                await websocket.send_json(formatted_metrics)

        except Exception as stream_error:
            logger.error(
                f"Error in real metrics streaming for VM {vm_id}: {stream_error}"
            )
            # Send error message
            await websocket.send_json(
                {
                    "type": "error",
                    "error": f"Metrics streaming error: {stream_error}",
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for VM {vm_id} metrics stream")
    except Exception as e:
        logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
        try:
            await websocket.send_json(
                {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )
        except:
            pass
    finally:
        manager.disconnect(websocket, vm_id)


async def execute_vm_command(
    vm_id: str, vm_type: str, command: str, working_dir: str = "/"
) -> dict[str, Any]:
    """Execute command on VM via SSH"""
    import asyncio

    try:
        if vm_type.lower() == "vagrant":
            # Execute command via vagrant ssh
            full_command = f"cd {working_dir} && {command}"

            # Use subprocess to execute vagrant ssh command
            process = await asyncio.create_subprocess_exec(
                "vagrant",
                "ssh",
                vm_id,
                "-c",
                full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/vagrant",
            )

            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)

            return {
                "stdout": stdout.decode("utf-8", errors="replace"),
                "stderr": stderr.decode("utf-8", errors="replace"),
                "exit_code": process.returncode or 0,
            }

        elif vm_type.lower() == "docker":
            # Execute command via docker exec
            process = await asyncio.create_subprocess_exec(
                "docker",
                "exec",
                "-w",
                working_dir,
                vm_id,
                "sh",
                "-c",
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)

            return {
                "stdout": stdout.decode("utf-8", errors="replace"),
                "stderr": stderr.decode("utf-8", errors="replace"),
                "exit_code": process.returncode or 0,
            }

        else:
            return {
                "stdout": "",
                "stderr": f"Unsupported VM type: {vm_type}",
                "exit_code": 1,
            }

    except TimeoutError:
        return {
            "stdout": "",
            "stderr": "Command execution timeout (30s)",
            "exit_code": 124,
        }
    except Exception as e:
        return {"stdout": "", "stderr": f"Command execution error: {e}", "exit_code": 1}


async def upload_file_to_vm(
    vm_id: str, vm_type: str, file_data: bytes, target_path: str
) -> dict[str, Any]:
    """Upload file to VM via SSH/SCP"""
    import os
    import tempfile

    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name

        try:
            if vm_type.lower() == "vagrant":
                # Upload via vagrant scp
                process = await asyncio.create_subprocess_exec(
                    "vagrant",
                    "upload",
                    temp_file_path,
                    target_path,
                    vm_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd="/vagrant",
                )

                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=60
                )

                if process.returncode == 0:
                    return {"success": True, "message": "File uploaded successfully"}
                else:
                    return {
                        "success": False,
                        "error": f"Upload failed: {stderr.decode()}",
                    }

            elif vm_type.lower() == "docker":
                # Upload via docker cp
                process = await asyncio.create_subprocess_exec(
                    "docker",
                    "cp",
                    temp_file_path,
                    f"{vm_id}:{target_path}",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                )

                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=60
                )

                if process.returncode == 0:
                    return {"success": True, "message": "File uploaded successfully"}
                else:
                    return {
                        "success": False,
                        "error": f"Upload failed: {stderr.decode()}",
                    }

            else:
                return {"success": False, "error": f"Unsupported VM type: {vm_type}"}

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except TimeoutError:
        return {"success": False, "error": "File upload timeout (60s)"}
    except Exception as e:
        return {"success": False, "error": f"Upload error: {e}"}


@router.websocket("/{vm_id}/commands/execute")
async def execute_command_stream(
    websocket: WebSocket, vm_id: str, vm_type: str = "docker"
):
    """Execute commands with real-time output streaming"""
    await manager.connect(websocket, f"{vm_id}_commands")

    try:
        # Send ready message
        await websocket.send_json(
            {"type": "ready", "message": "Command execution ready", "vm_id": vm_id}
        )

        while True:
            # Wait for command from client
            data = await websocket.receive_text()
            command_data = json.loads(data)

            command = command_data.get("command", "")
            working_dir = command_data.get("working_directory", "/tmp")

            if not command:
                await websocket.send_json(
                    {"type": "error", "message": "No command provided"}
                )
                continue

            # Real command execution via SSH/Vagrant
            await websocket.send_json(
                {
                    "type": "command_start",
                    "command": command,
                    "working_directory": working_dir,
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )

            try:
                # Execute real command via SSH
                result = await execute_vm_command(vm_id, vm_type, command, working_dir)

                await websocket.send_json(
                    {
                        "type": "command_output",
                        "stdout": result.get("stdout", ""),
                        "stderr": result.get("stderr", ""),
                        "exit_code": result.get("exit_code", 0),
                        "is_complete": True,
                        "timestamp": datetime.now(UTC).isoformat(),
                    }
                )

            except Exception as cmd_error:
                logger.error(f"Command execution error for VM {vm_id}: {cmd_error}")
                await websocket.send_json(
                    {
                        "type": "command_output",
                        "stdout": "",
                        "stderr": f"Command execution failed: {cmd_error}",
                        "exit_code": 1,
                        "is_complete": True,
                        "timestamp": datetime.now(UTC).isoformat(),
                    }
                )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for VM {vm_id} command execution")
    except Exception as e:
        logger.error(f"Error in command execution for VM {vm_id}: {e}")
        try:
            await websocket.send_json(
                {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )
        except:
            pass
    finally:
        manager.disconnect(websocket, f"{vm_id}_commands")


@router.websocket("/{vm_id}/files/upload")
async def upload_file_stream(websocket: WebSocket, vm_id: str, vm_type: str = "docker"):
    """Upload file to VM with real-time progress via WebSocket"""
    await websocket.accept()

    try:
        # Send ready message
        await websocket.send_json(
            {
                "type": "upload_ready",
                "message": "Ready to receive file upload",
                "vm_id": vm_id,
            }
        )

        # Wait for file upload initiation
        data = await websocket.receive_text()
        upload_info = json.loads(data)

        target_path = upload_info.get("target_path", "/tmp/uploaded_file")

        # Real file upload implementation
        try:
            # Wait for file data
            file_data = await websocket.receive_bytes()

            # Report upload progress
            await websocket.send_json(
                {
                    "type": "upload_progress",
                    "progress": 50,
                    "target_path": target_path,
                    "message": "Uploading file to VM...",
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )

            # Upload file to VM
            upload_result = await upload_file_to_vm(
                vm_id, vm_type, file_data, target_path
            )

            if upload_result["success"]:
                await websocket.send_json(
                    {
                        "type": "upload_complete",
                        "target_path": target_path,
                        "message": "File upload completed successfully",
                        "file_size": len(file_data),
                        "timestamp": datetime.now(UTC).isoformat(),
                    }
                )
            else:
                await websocket.send_json(
                    {
                        "type": "error",
                        "error": upload_result["error"],
                        "timestamp": datetime.now(UTC).isoformat(),
                    }
                )

        except Exception as upload_error:
            await websocket.send_json(
                {
                    "type": "error",
                    "error": f"File upload failed: {upload_error}",
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for VM {vm_id} file upload")
    except Exception as e:
        logger.error(f"Error in file upload for VM {vm_id}: {e}")
        try:
            await websocket.send_json(
                {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )
        except:
            pass


@router.websocket("/test-simple")
async def test_simple_websocket(websocket: WebSocket):
    """Simple test WebSocket endpoint in VMs router"""
    await websocket.accept()
    await websocket.send_text("Hello from VMs router!")
    await websocket.close()
