"""Administrative endpoints for TurdParty API."""

import asyncio
from datetime import datetime
import logging
import os
from pathlib import Path
from typing import Any

from fastapi import APIRouter, BackgroundTasks, HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin", tags=["admin"])

# Project root for script execution
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent.parent.parent


class AdminResponse(BaseModel):
    """Response model for admin operations."""

    success: bool
    message: str
    task_id: str | None = None
    timestamp: str
    details: dict[str, Any] | None = None


class DocsRebuildRequest(BaseModel):
    """Request model for documentation rebuild."""

    clean: bool = False
    extract_api: bool = True
    restart_frontend: bool = True


class SphinxReportRequest(BaseModel):
    """Request model for Sphinx report generation."""

    file_uuid: str | None = None
    binary_name: str | None = None
    report_type: str = "nodejs"  # nodejs, binary, comprehensive
    include_ecs_logs: bool = True
    build_html: bool = True


async def rebuild_documentation_task(
    clean: bool = False, extract_api: bool = True, restart_frontend: bool = True
):
    """Background task to rebuild documentation."""
    try:
        logger.info("Starting documentation rebuild task")

        # Build documentation
        cmd = ["bash", str(PROJECT_ROOT / "scripts" / "build-docs-auto.sh"), "build"]
        if clean:
            cmd.append("--clean")

        result = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=PROJECT_ROOT,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )

        stdout, stderr = await result.communicate()

        if result.returncode != 0:
            logger.error(f"Documentation build failed: {stderr.decode()}")
            return False

        logger.info("Documentation build completed successfully")

        # Extract API documentation if requested
        if extract_api:
            try:
                import httpx

                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        "http://localhost:8000/openapi.json", timeout=10
                    )
                    if response.status_code == 200:
                        api_docs_path = (
                            PROJECT_ROOT
                            / "docs"
                            / "_build"
                            / "html"
                            / "api"
                            / "openapi.json"
                        )
                        api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                        with open(api_docs_path, "w") as f:
                            f.write(response.text)

                        logger.info("API documentation extracted successfully")
            except Exception as e:
                logger.warning(f"Failed to extract API documentation: {e}")

        # Restart frontend if requested
        if restart_frontend:
            try:
                restart_cmd = ["docker-compose", "restart", "frontend"]
                restart_result = await asyncio.create_subprocess_exec(
                    *restart_cmd,
                    cwd=PROJECT_ROOT,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                )

                await restart_result.communicate()

                if restart_result.returncode == 0:
                    logger.info("Frontend service restarted successfully")
                else:
                    logger.warning("Failed to restart frontend service")

            except Exception as e:
                logger.warning(f"Failed to restart frontend: {e}")

        return True

    except Exception as e:
        logger.error(f"Documentation rebuild task failed: {e}")
        return False


async def generate_sphinx_report_task(
    file_uuid: str | None = None,
    binary_name: str | None = None,
    report_type: str = "nodejs",
    include_ecs_logs: bool = True,
    build_html: bool = True,
):
    """Background task to generate Sphinx reports from ECS logs."""
    try:
        logger.info(f"Starting Sphinx report generation task for {report_type}")

        # Determine which script to run based on report type
        if report_type == "nodejs":
            script_name = "generate-nodejs-sphinx-report.py"
        elif report_type == "binary" and file_uuid:
            script_name = "generate-sphinx-10-binary-report.py"
        else:
            script_name = "generate-nodejs-sphinx-report.py"  # Default

        # Build command
        cmd = ["python", str(PROJECT_ROOT / "scripts" / script_name)]

        # Add arguments if provided
        env = {}
        if file_uuid:
            env["TURDPARTY_FILE_UUID"] = file_uuid
        if binary_name:
            env["TURDPARTY_BINARY_NAME"] = binary_name

        result = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=PROJECT_ROOT,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={**env, **dict(os.environ)} if env else None,
        )

        stdout, stderr = await result.communicate()

        if result.returncode != 0:
            logger.error(f"Sphinx report generation failed: {stderr.decode()}")
            return False

        logger.info("Sphinx report generation completed successfully")
        return True

    except Exception as e:
        logger.error(f"Sphinx report generation failed: {e}")
        return False


@router.post("/docs/rebuild", response_model=AdminResponse)
async def rebuild_docs(request: DocsRebuildRequest, background_tasks: BackgroundTasks):
    """
    Trigger documentation rebuild.

    This endpoint rebuilds the Sphinx documentation, optionally extracts
    API documentation, and restarts the frontend service to serve updated docs.
    """
    try:
        import uuid

        task_id = str(uuid.uuid4())

        # Add background task
        background_tasks.add_task(
            rebuild_documentation_task,
            request.clean,
            request.extract_api,
            request.restart_frontend,
        )

        logger.info(f"Documentation rebuild task {task_id} queued")

        return AdminResponse(
            success=True,
            message="Documentation rebuild task queued successfully",
            task_id=task_id,
            timestamp=datetime.now(datetime.UTC).isoformat(),
            details={
                "clean": request.clean,
                "extract_api": request.extract_api,
                "restart_frontend": request.restart_frontend,
            },
        )

    except Exception as e:
        logger.error(f"Failed to queue documentation rebuild: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue documentation rebuild: {e!s}",
        )


@router.post("/reports/sphinx/generate", response_model=AdminResponse)
async def generate_sphinx_report(
    request: SphinxReportRequest, background_tasks: BackgroundTasks
):
    """
    Generate Sphinx documentation from ECS logs and analysis data.

    This endpoint triggers generation of comprehensive Sphinx documentation
    from ECS logs, analysis data, and binary execution results. Supports
    different report types including Node.js specific reports.
    """
    try:
        import uuid

        task_id = str(uuid.uuid4())

        # Add background task
        background_tasks.add_task(
            generate_sphinx_report_task,
            request.file_uuid,
            request.binary_name,
            request.report_type,
            request.include_ecs_logs,
            request.build_html,
        )

        logger.info(f"Sphinx report generation task {task_id} queued")

        return AdminResponse(
            success=True,
            message=f"Sphinx {request.report_type} report generation task queued successfully",
            task_id=task_id,
            timestamp=datetime.now(datetime.UTC).isoformat(),
            details={
                "file_uuid": request.file_uuid,
                "binary_name": request.binary_name,
                "report_type": request.report_type,
                "include_ecs_logs": request.include_ecs_logs,
                "build_html": request.build_html,
            },
        )

    except Exception as e:
        logger.error(f"Failed to queue Sphinx report generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Sphinx report generation failed: {e!s}",
        )


@router.post("/services/{service_name}/restart", response_model=AdminResponse)
async def restart_service(service_name: str, background_tasks: BackgroundTasks):
    """
    Restart a specific service.

    Restarts the specified Docker Compose service.
    """
    try:
        import uuid

        task_id = str(uuid.uuid4())

        async def restart_task():
            try:
                result = await asyncio.create_subprocess_exec(
                    "docker-compose",
                    "restart",
                    service_name,
                    cwd=PROJECT_ROOT,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                )

                stdout, stderr = await result.communicate()

                if result.returncode == 0:
                    logger.info(f"Service {service_name} restarted successfully")
                else:
                    logger.error(f"Failed to restart {service_name}: {stderr.decode()}")

            except Exception as e:
                logger.error(f"Service restart task failed: {e}")

        background_tasks.add_task(restart_task)

        return AdminResponse(
            success=True,
            message=f"Service {service_name} restart task queued",
            task_id=task_id,
            timestamp=datetime.now(datetime.UTC).isoformat(),
            details={"service": service_name},
        )

    except Exception as e:
        logger.error(f"Failed to queue service restart: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue service restart: {e!s}",
        )


@router.get("/health")
async def admin_health():
    """Admin endpoint health check."""
    return {
        "status": "healthy",
        "service": "admin-api",
        "timestamp": datetime.now(datetime.UTC).isoformat(),
    }
